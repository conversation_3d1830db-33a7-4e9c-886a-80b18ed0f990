#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真正的流式传输测试脚本
测试新的实时流式传输功能
"""

import asyncio
import json
import logging
import time
import websockets
from datetime import datetime
from typing import Dict, List, Optional
import requests

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('realtime_streaming_test.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

# 配置
API_BASE_URL = "http://localhost:8000/api/v1"
WS_BASE_URL = "ws://localhost:8000"


class RealtimeStreamingTester:
    """实时流式传输测试器"""
    
    def __init__(self):
        self.access_token = None
        self.novel_id = None
        self.task_id = None
        self.received_chunks = []
        self.streaming_metrics = {
            "start_time": None,
            "first_chunk_time": None,
            "last_chunk_time": None,
            "total_chunks": 0,
            "total_characters": 0,
            "stages_completed": 0
        }
    
    async def run_test(self):
        """运行完整的流式传输测试"""
        logger.info("🚀 开始真正的流式传输测试")
        
        try:
            # 1. 登录获取token
            if not await self.login():
                return False
            
            # 2. 创建小说
            if not await self.create_novel():
                return False
            
            # 3. 启动架构生成任务
            if not await self.start_architecture_generation():
                return False
            
            # 4. 连接WebSocket并监控实时流式传输
            await self.monitor_realtime_streaming()
            
            # 5. 分析测试结果
            self.analyze_results()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 测试失败: {e}")
            return False
    
    async def login(self) -> bool:
        """登录获取访问令牌"""
        try:
            response = requests.post(f"{API_BASE_URL}/auth/login", json={
                "username": "test_user",
                "password": "test_password"
            })
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    self.access_token = data["data"]["access_token"]
                    logger.info("✅ 登录成功")
                    return True
            
            logger.error(f"❌ 登录失败: {response.status_code}")
            return False
            
        except Exception as e:
            logger.error(f"❌ 登录异常: {e}")
            return False
    
    async def create_novel(self) -> bool:
        """创建测试小说"""
        try:
            headers = {"Authorization": f"Bearer {self.access_token}"}
            novel_data = {
                "title": "实时流式传输测试小说",
                "description": "测试真正的实时流式传输功能，验证内容是否能够实时显示",
                "genre": "science_fiction",
                "target_length": 5000,
                "style_settings": {
                    "style": "实时科幻",
                    "pace": "fast",
                    "focus": "realtime_streaming"
                }
            }
            
            response = requests.post(f"{API_BASE_URL}/novels/", 
                                   json=novel_data, headers=headers)
            
            if response.status_code == 201:
                data = response.json()
                if data.get("success"):
                    self.novel_id = data["data"]["id"]
                    logger.info(f"✅ 小说创建成功，ID: {self.novel_id}")
                    return True
            
            logger.error(f"❌ 小说创建失败: {response.status_code}")
            return False
            
        except Exception as e:
            logger.error(f"❌ 小说创建异常: {e}")
            return False
    
    async def start_architecture_generation(self) -> bool:
        """启动架构生成任务"""
        try:
            headers = {"Authorization": f"Bearer {self.access_token}"}
            generation_data = {
                "novel_id": self.novel_id,
                "theme": "2099年，AI与人类协作的实时冒险故事",
                "genre": "science_fiction",
                "target_length": 5000,
                "style_preferences": {
                    "style": "实时科幻",
                    "pace": "fast",
                    "focus": "realtime_experience"
                }
            }
            
            response = requests.post(f"{API_BASE_URL}/generation/architecture", 
                                   json=generation_data, headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    self.task_id = data["data"]["task_id"]
                    logger.info(f"✅ 架构生成任务启动成功，任务ID: {self.task_id}")
                    return True
            
            logger.error(f"❌ 架构生成任务启动失败: {response.status_code}")
            return False
            
        except Exception as e:
            logger.error(f"❌ 架构生成任务启动异常: {e}")
            return False
    
    async def monitor_realtime_streaming(self):
        """监控实时流式传输"""
        ws_url = f"{WS_BASE_URL}/ws/{self.task_id}"
        logger.info(f"🔌 连接WebSocket进行实时监控: {ws_url}")
        
        self.streaming_metrics["start_time"] = time.time()
        
        try:
            async with websockets.connect(ws_url) as websocket:
                logger.info("✅ WebSocket连接成功，开始监控实时流式传输")
                
                # 发送心跳包
                await websocket.send(json.dumps({
                    "type": "ping",
                    "timestamp": datetime.now().isoformat()
                }))
                
                # 监听实时消息
                async for message in websocket:
                    try:
                        data = json.loads(message)
                        await self.handle_streaming_message(data)
                        
                        # 检查是否完成
                        if self.is_streaming_complete(data):
                            logger.info("🏁 流式传输完成")
                            break
                            
                    except json.JSONDecodeError as e:
                        logger.error(f"❌ JSON解析失败: {e}")
                        
        except Exception as e:
            logger.error(f"❌ WebSocket连接异常: {e}")
    
    async def handle_streaming_message(self, data: Dict):
        """处理流式传输消息"""
        message_type = data.get("type")
        current_time = time.time()
        
        if message_type == "pong":
            logger.info("💓 心跳响应收到")
            
        elif message_type == "connection_status":
            logger.info(f"🔗 连接状态: {data.get('status')}")
            
        elif message_type == "stage_start":
            stage = data.get("stage")
            logger.info(f"🎬 开始阶段: {stage}")
            
        elif message_type == "content_chunk":
            # 这是真正的实时内容片段！
            chunk = data.get("chunk", "")
            stage = data.get("stage")
            chunk_index = data.get("chunk_index", 0)
            total_length = data.get("total_length", 0)
            
            # 记录第一个内容片段的时间
            if self.streaming_metrics["first_chunk_time"] is None:
                self.streaming_metrics["first_chunk_time"] = current_time
                first_chunk_delay = current_time - self.streaming_metrics["start_time"]
                logger.info(f"⚡ 首个内容片段到达，延迟: {first_chunk_delay:.2f}秒")
            
            self.received_chunks.append({
                "stage": stage,
                "chunk_index": chunk_index,
                "chunk": chunk,
                "total_length": total_length,
                "timestamp": current_time
            })
            
            self.streaming_metrics["total_chunks"] += 1
            self.streaming_metrics["total_characters"] += len(chunk)
            self.streaming_metrics["last_chunk_time"] = current_time
            
            # 实时显示内容片段
            logger.info(f"📝 [{stage}] 片段{chunk_index}: {chunk[:50]}{'...' if len(chunk) > 50 else ''}")
            
        elif message_type == "content_complete":
            stage = data.get("stage")
            final_content = data.get("final_content", "")
            self.streaming_metrics["stages_completed"] += 1
            logger.info(f"✅ 阶段完成: {stage}, 最终内容长度: {len(final_content)}")
            
        elif message_type == "task_update":
            status = data.get("status")
            progress = data.get("progress", 0)
            message = data.get("message", "")
            logger.info(f"📊 任务更新: {status} ({progress}%) - {message}")
            
        elif message_type == "error_recovery":
            error_message = data.get("error_message")
            recovery_action = data.get("recovery_action")
            logger.warning(f"🔧 错误恢复: {error_message} -> {recovery_action}")
    
    def is_streaming_complete(self, data: Dict) -> bool:
        """检查流式传输是否完成"""
        message_type = data.get("type")
        
        if message_type == "task_update":
            status = data.get("status")
            return status in ["SUCCESS", "FAILED", "CANCELLED"]
        
        return False
    
    def analyze_results(self):
        """分析测试结果"""
        logger.info("\n" + "="*60)
        logger.info("📊 流式传输测试结果分析")
        logger.info("="*60)
        
        if not self.streaming_metrics["first_chunk_time"]:
            logger.error("❌ 未收到任何内容片段，流式传输失败")
            return
        
        # 计算关键指标
        total_duration = self.streaming_metrics["last_chunk_time"] - self.streaming_metrics["start_time"]
        first_chunk_delay = self.streaming_metrics["first_chunk_time"] - self.streaming_metrics["start_time"]
        streaming_duration = self.streaming_metrics["last_chunk_time"] - self.streaming_metrics["first_chunk_time"]
        
        chunks_per_second = self.streaming_metrics["total_chunks"] / streaming_duration if streaming_duration > 0 else 0
        chars_per_second = self.streaming_metrics["total_characters"] / streaming_duration if streaming_duration > 0 else 0
        
        logger.info(f"⏱️  总耗时: {total_duration:.2f}秒")
        logger.info(f"⚡ 首片段延迟: {first_chunk_delay:.2f}秒")
        logger.info(f"🔄 流式传输时长: {streaming_duration:.2f}秒")
        logger.info(f"📦 总片段数: {self.streaming_metrics['total_chunks']}")
        logger.info(f"📝 总字符数: {self.streaming_metrics['total_characters']}")
        logger.info(f"🎭 完成阶段数: {self.streaming_metrics['stages_completed']}")
        logger.info(f"📈 片段速率: {chunks_per_second:.2f} 片段/秒")
        logger.info(f"✍️  字符速率: {chars_per_second:.2f} 字符/秒")
        
        # 评估流式传输质量
        if first_chunk_delay < 5.0:
            logger.info("🎉 优秀！首片段延迟小于5秒")
        elif first_chunk_delay < 10.0:
            logger.info("👍 良好！首片段延迟小于10秒")
        else:
            logger.warning("⚠️ 首片段延迟较长，需要优化")
        
        if chunks_per_second > 2.0:
            logger.info("🚀 优秀！流式传输速度很快")
        elif chunks_per_second > 1.0:
            logger.info("✅ 良好！流式传输速度正常")
        else:
            logger.warning("🐌 流式传输速度较慢")
        
        logger.info("="*60)


async def main():
    """主函数"""
    tester = RealtimeStreamingTester()
    success = await tester.run_test()
    
    if success:
        logger.info("🎉 实时流式传输测试完成")
    else:
        logger.error("💥 实时流式传输测试失败")
    
    return 0 if success else 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        exit(exit_code)
    except KeyboardInterrupt:
        logger.info("👋 测试被用户中断")
        exit(1)
