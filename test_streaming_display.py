#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
AI小说生成器 - 流式传输显示测试
专门用于测试和展示流式内容传输功能
"""

import asyncio
import json
import time
import requests
import websockets
from datetime import datetime
from typing import Dict, Optional
from dataclasses import dataclass


@dataclass
class StreamingTestConfig:
    """流式测试配置"""
    api_base_url: str = "http://localhost:8000/api/v1"
    ws_base_url: str = "ws://localhost:8000"
    test_wx_code: str = "test_code_123456"
    test_nickname: str = "AI小说测试用户"
    test_avatar: str = "https://example.com/avatar.jpg"
    novel_theme: str = "一个现代程序员意外穿越到魔法世界，必须用编程思维拯救这个被古老诅咒笼罩的王国"
    novel_genre: str = "fantasy"
    target_length: int = 25000


class StreamingDisplayTester:
    """流式显示测试器"""
    
    def __init__(self, config: StreamingTestConfig):
        self.config = config
        self.access_token: Optional[str] = None
        self.user_info: Optional[Dict] = None
        self.novel_id: Optional[str] = None
        self.current_task_id: Optional[str] = None
        
        # 流式内容缓存
        self.streaming_content: Dict[str, str] = {}
        self.stage_progress: Dict[str, int] = {}
        
    def _log(self, message: str, level: str = "INFO"):
        """格式化日志输出"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        level_emoji = {
            "INFO": "ℹ️",
            "SUCCESS": "✅", 
            "ERROR": "❌",
            "WARNING": "⚠️",
            "STREAM": "📡"
        }
        emoji = level_emoji.get(level, "📝")
        print(f"[{timestamp}] {emoji} {message}")
    
    async def login(self) -> bool:
        """用户登录"""
        self._log("开始用户登录...")
        
        try:
            response = requests.post(
                f"{self.config.api_base_url}/auth/test/login",
                json={
                    "code": self.config.test_wx_code,
                    "nickname": self.config.test_nickname,
                    "avatar_url": self.config.test_avatar
                }
            )
            
            if response.status_code == 200:
                data = response.json()
                self.access_token = data["access_token"]
                self.user_info = data["user"]
                self._log(f"登录成功! 用户ID: {self.user_info['id']}", "SUCCESS")
                return True
            else:
                self._log(f"登录失败: {response.status_code}", "ERROR")
                return False
                
        except Exception as e:
            self._log(f"登录异常: {e}", "ERROR")
            return False
    
    async def create_novel(self) -> bool:
        """创建小说"""
        self._log("创建小说项目...")
        
        try:
            response = requests.post(
                f"{self.config.api_base_url}/novels/",
                headers={"Authorization": f"Bearer {self.access_token}"},
                json={
                    "title": "AI奇幻冒险小说",
                    "description": self.config.novel_theme,
                    "genre": self.config.novel_genre,
                    "target_length": self.config.target_length
                }
            )
            
            if response.status_code == 200:
                data = response.json()
                if data["success"]:
                    self.novel_id = data["data"]["id"]
                    self._log(f"小说创建成功! ID: {self.novel_id}", "SUCCESS")
                    return True
            
            self._log(f"小说创建失败: {response.status_code}", "ERROR")
            return False
            
        except Exception as e:
            self._log(f"小说创建异常: {e}", "ERROR")
            return False
    
    async def start_architecture_generation(self) -> Optional[str]:
        """启动架构生成"""
        self._log("启动架构生成任务...")
        
        try:
            response = requests.post(
                f"{self.config.api_base_url}/generation/architecture",
                headers={"Authorization": f"Bearer {self.access_token}"},
                json={
                    "novel_id": self.novel_id,
                    "theme": self.config.novel_theme,
                    "genre": self.config.novel_genre,
                    "target_length": self.config.target_length
                }
            )
            
            if response.status_code == 200:
                data = response.json()
                if data["success"]:
                    task_id = data["data"]["task_id"]
                    self.current_task_id = task_id
                    self._log(f"架构生成任务启动! 任务ID: {task_id}", "SUCCESS")
                    return task_id
            
            self._log(f"任务启动失败: {response.status_code}", "ERROR")
            return None
            
        except Exception as e:
            self._log(f"任务启动异常: {e}", "ERROR")
            return None
    
    async def handle_websocket_message(self, data: Dict):
        """处理WebSocket消息"""
        message_type = data.get("type", "unknown")
        
        if message_type == "task_update":
            status = data.get("status", "")
            progress = data.get("progress", 0)
            message = data.get("message", "")
            current_stage = data.get("current_stage", "")
            
            stage_info = f" [{current_stage}]" if current_stage else ""
            self._log(f"任务进度{stage_info}: {progress}% - {message}")
            
        elif message_type == "stage_start":
            stage = data.get("stage", "")
            self._log(f"🎬 开始生成阶段: {stage}", "STREAM")
            self.streaming_content[stage] = ""
            self.stage_progress[stage] = 0
            
        elif message_type == "content_chunk":
            stage = data.get("stage", "")
            chunk = data.get("chunk", "")
            chunk_index = data.get("chunk_index", 0)
            total_length = data.get("total_length", 0)
            
            # 累积内容
            if stage not in self.streaming_content:
                self.streaming_content[stage] = ""
            self.streaming_content[stage] += chunk
            
            # 显示流式内容（实时更新）
            self._display_streaming_content(stage, chunk, chunk_index, total_length)
            
        elif message_type == "content_complete":
            stage = data.get("stage", "")
            final_content = data.get("final_content", "")
            
            self._log(f"✅ 阶段完成: {stage} (共{len(final_content)}字符)", "SUCCESS")
            self._display_stage_summary(stage, final_content)
            
        elif message_type == "generation_complete":
            summary = data.get("summary", "")
            result = data.get("result", {})
            
            self._log("🎉 生成任务完成!", "SUCCESS")
            if summary:
                self._log(f"📋 {summary}", "SUCCESS")
            
            self._display_final_summary(result)
            
        elif message_type == "error_recovery":
            error_message = data.get("error_message", "")
            recovery_action = data.get("recovery_action", "")
            self._log(f"错误恢复: {error_message}", "WARNING")
            if recovery_action:
                self._log(f"恢复操作: {recovery_action}", "WARNING")
    
    def _display_streaming_content(self, stage: str, chunk: str, chunk_index: int, total_length: int):
        """显示流式内容"""
        # 限制显示长度，避免输出过长
        if len(chunk) > 50:
            display_chunk = chunk[:50] + "..."
        else:
            display_chunk = chunk
        
        # 显示当前片段
        self._log(f"[{stage}] 片段{chunk_index}: {display_chunk}", "STREAM")
        
        # 显示累积进度
        current_length = len(self.streaming_content.get(stage, ""))
        if total_length > 0:
            progress = min(100, int(current_length / total_length * 100))
            self._log(f"[{stage}] 进度: {current_length}/{total_length} ({progress}%)", "STREAM")
    
    def _display_stage_summary(self, stage: str, final_content: str):
        """显示阶段摘要"""
        content_length = len(final_content)
        self._log(f"📄 [{stage}] 最终长度: {content_length}字符")
        
        # 显示内容预览
        if final_content:
            preview_length = min(200, len(final_content))
            preview = final_content[:preview_length]
            if len(final_content) > preview_length:
                preview += "..."
            self._log(f"📖 [{stage}] 内容预览: {preview}")
        
        print("-" * 60)
    
    def _display_final_summary(self, result: Dict):
        """显示最终摘要"""
        print("=" * 60)
        self._log("📊 生成结果统计:", "SUCCESS")
        
        if "word_count" in result:
            self._log(f"   总字数: {result['word_count']}")
        
        if "components" in result:
            components = result["components"]
            for component_name, content in components.items():
                if content:
                    self._log(f"   {component_name}: {len(content)}字符")
        
        print("=" * 60)
    
    async def monitor_with_websocket(self, task_id: str, timeout: int = 600):
        """使用WebSocket监控任务"""
        ws_url = f"{self.config.ws_base_url}/ws/{task_id}"
        self._log(f"连接WebSocket: {ws_url}", "STREAM")
        
        try:
            async with websockets.connect(ws_url) as websocket:
                self._log("WebSocket连接成功!", "SUCCESS")
                
                # 发送心跳包
                await websocket.send(json.dumps({
                    "type": "ping",
                    "timestamp": datetime.now().isoformat()
                }))
                
                start_time = time.time()
                
                async for message in websocket:
                    try:
                        data = json.loads(message)
                        await self.handle_websocket_message(data)
                        
                        # 检查任务是否完成
                        if data.get("type") == "task_update":
                            status = data.get("status", "")
                            if status in ["SUCCESS", "FAILED", "CANCELLED"]:
                                self._log(f"任务结束，状态: {status}", "SUCCESS" if status == "SUCCESS" else "ERROR")
                                break
                        
                        # 检查超时
                        if time.time() - start_time > timeout:
                            self._log("监控超时", "WARNING")
                            break
                            
                    except json.JSONDecodeError as e:
                        self._log(f"JSON解析失败: {e}", "ERROR")
                        
        except Exception as e:
            self._log(f"WebSocket连接异常: {e}", "ERROR")
    
    async def run_streaming_test(self):
        """运行流式测试"""
        print("🚀 AI小说生成器 - 流式传输显示测试")
        print("=" * 60)
        print(f"📡 API地址: {self.config.api_base_url}")
        print(f"🔌 WebSocket地址: {self.config.ws_base_url}")
        print(f"📖 测试主题: {self.config.novel_theme}")
        print("=" * 60)
        
        # 步骤1: 登录
        if not await self.login():
            return False
        
        # 步骤2: 创建小说
        if not await self.create_novel():
            return False
        
        # 步骤3: 启动架构生成
        task_id = await self.start_architecture_generation()
        if not task_id:
            return False
        
        # 步骤4: WebSocket监控
        self._log("开始WebSocket实时监控...", "STREAM")
        await self.monitor_with_websocket(task_id)
        
        self._log("流式测试完成!", "SUCCESS")
        return True


async def main():
    """主函数"""
    config = StreamingTestConfig()
    tester = StreamingDisplayTester(config)
    
    try:
        await tester.run_streaming_test()
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")


if __name__ == "__main__":
    asyncio.run(main())
