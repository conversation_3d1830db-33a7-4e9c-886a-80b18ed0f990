{"timestamp": "2025-06-19T08:53:31.463854Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59602 - \"GET /health HTTP/1.1\" 200", "request_id": "661adbd6", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T08:53:32.058859Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59604 - \"POST /api/v1/auth/wechat/login HTTP/1.1\" 401", "request_id": "468a41e4", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:03:29.066720Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:60445 - \"GET /health HTTP/1.1\" 200", "request_id": "d1320f93", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:03:29.732004Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:60447 - \"POST /api/v1/auth/wechat/login HTTP/1.1\" 401", "request_id": "1e1c0974", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:14:17.962724Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:61217 - \"GET /health HTTP/1.1\" 200", "request_id": "9154ce59", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:17:31.358672Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:61510 - \"GET /health HTTP/1.1\" 200", "request_id": "e6b47afe", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:17:31.667019Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:61512 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "30285ff8", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:19:58.969043Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:61903 - \"GET /health HTTP/1.1\" 200", "request_id": "6808e81b", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:19:59.240250Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:61905 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "2bb8c453", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:19:59.259882Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:61905 - \"POST /api/v1/novels/ HTTP/1.1\" 500", "request_id": "3e47bb57", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:20:28.115610Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:61928 - \"GET /health HTTP/1.1\" 200", "request_id": "aadfb2e0", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:20:28.387887Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:61930 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "772b1862", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:20:28.401216Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:61930 - \"POST /api/v1/novels/ HTTP/1.1\" 500", "request_id": "d3c5f51e", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:22:32.994161Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62071 - \"GET /health HTTP/1.1\" 200", "request_id": "b3bdcd46", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:22:33.265712Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62073 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "563a57d3", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:22:33.316354Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62073 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "00f379e3", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:22:58.169744Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62108 - \"GET /health HTTP/1.1\" 200", "request_id": "752f1514", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:22:58.436880Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62110 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "8d5ce7f5", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:22:58.477568Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62110 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "ff864ce6", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:26:06.678225Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62341 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "41763a4c", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:26:06.715355Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62341 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "7225b12d", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:26:45.828919Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62376 - \"GET /health HTTP/1.1\" 200", "request_id": "d66a42dd", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:26:46.101252Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62379 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "0e42d347", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:26:46.143322Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62379 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "d547b78e", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:30:19.789256Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62617 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "9308cf86", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:30:19.827499Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62617 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "c7de4cca", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:30:19.933567Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62617 - \"POST /api/v1/generation/architecture HTTP/1.1\" 200", "request_id": "fb814081", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:31:24.054145Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62730 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "0afaabd6", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:31:24.093598Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62730 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "d543d96a", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:31:24.184844Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62730 - \"POST /api/v1/generation/architecture HTTP/1.1\" 200", "request_id": "f9f8f118", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:31:24.198874Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62730 - \"GET /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2 HTTP/1.1\" 500", "request_id": "a4dc017d", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:31:29.211027Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62730 - \"GET /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2 HTTP/1.1\" 500", "request_id": "9d112869", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:31:34.501714Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62738 - \"GET /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2 HTTP/1.1\" 500", "request_id": "515abed9", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:31:39.500472Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62739 - \"GET /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2 HTTP/1.1\" 500", "request_id": "7566d688", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:31:44.782291Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62757 - \"GET /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2 HTTP/1.1\" 500", "request_id": "c5079f37", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:31:49.800725Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62769 - \"GET /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2 HTTP/1.1\" 500", "request_id": "82fecd0c", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:31:54.824485Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62769 - \"GET /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2 HTTP/1.1\" 500", "request_id": "694b2854", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:32:00.107520Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62776 - \"GET /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2 HTTP/1.1\" 500", "request_id": "ec4716f0", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:32:05.129037Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62777 - \"GET /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2 HTTP/1.1\" 500", "request_id": "6f4cd954", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:32:10.128866Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62777 - \"GET /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2 HTTP/1.1\" 500", "request_id": "661b39e1", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:32:15.145986Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62777 - \"GET /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2 HTTP/1.1\" 500", "request_id": "d1e377c0", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:32:20.163698Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62777 - \"GET /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2 HTTP/1.1\" 500", "request_id": "6262e36a", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:32:25.451441Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62781 - \"GET /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2 HTTP/1.1\" 500", "request_id": "38c1d40d", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:32:30.460041Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62782 - \"GET /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2 HTTP/1.1\" 500", "request_id": "0716cb18", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:32:35.478260Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62782 - \"GET /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2 HTTP/1.1\" 500", "request_id": "20e39998", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:32:40.496017Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62782 - \"GET /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2 HTTP/1.1\" 500", "request_id": "2a3a99a0", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:32:45.507677Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62782 - \"GET /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2 HTTP/1.1\" 500", "request_id": "c40e6bd1", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:32:50.779925Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62801 - \"GET /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2 HTTP/1.1\" 500", "request_id": "15e4c6ff", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:32:55.781693Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62803 - \"GET /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2 HTTP/1.1\" 500", "request_id": "b1188cd3", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:33:01.058433Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62805 - \"GET /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2 HTTP/1.1\" 500", "request_id": "0efaa0d5", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:33:06.076665Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62809 - \"GET /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2 HTTP/1.1\" 500", "request_id": "eca89849", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:33:11.352066Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62812 - \"GET /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2 HTTP/1.1\" 500", "request_id": "f93137d8", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:33:16.370065Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62812 - \"GET /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2 HTTP/1.1\" 500", "request_id": "10518682", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:33:23.043539Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62820 - \"GET /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2 HTTP/1.1\" 500", "request_id": "f703f3f8", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:33:28.040664Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62828 - \"GET /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2 HTTP/1.1\" 500", "request_id": "42b48975", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:33:33.334289Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62844 - \"GET /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2 HTTP/1.1\" 500", "request_id": "dda53d7b", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:33:38.351551Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62844 - \"GET /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2 HTTP/1.1\" 500", "request_id": "6735212e", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:33:43.369571Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62844 - \"GET /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2 HTTP/1.1\" 500", "request_id": "23d2f5bc", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:33:48.657199Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62863 - \"GET /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2 HTTP/1.1\" 500", "request_id": "41747719", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:33:53.680143Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62863 - \"GET /api/v1/tasks/a788917a-76e1-46c0-a919-12a813b536e2 HTTP/1.1\" 500", "request_id": "9d4e2990", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:40:16.442770Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63395 - \"GET /health HTTP/1.1\" 200", "request_id": "e9edde57", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:40:16.714731Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63397 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "fb2a70f8", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:40:16.755243Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63397 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "e5dbf49b", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:40:16.841592Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63397 - \"POST /api/v1/generation/architecture HTTP/1.1\" 200", "request_id": "fe66283c", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:40:16.859322Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63397 - \"GET /api/v1/tasks/a707a38f-7456-4471-8499-14592dc3f957 HTTP/1.1\" 404", "request_id": "510edcf8", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:40:21.870559Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63397 - \"GET /api/v1/tasks/a707a38f-7456-4471-8499-14592dc3f957 HTTP/1.1\" 404", "request_id": "853bd845", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:41:54.518590Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63528 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "433c158d", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:41:54.565054Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63528 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "4197ef10", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:41:54.655162Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63528 - \"POST /api/v1/generation/architecture HTTP/1.1\" 200", "request_id": "2fd0d03a", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T09:41:54.674101Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63528 - \"GET /api/v1/tasks/6aac93dc-e0ca-481c-881e-5e7bfa522267 HTTP/1.1\" 404", "request_id": "a73f8fcc", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T15:26:02.356383Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:58667 - \"GET /health HTTP/1.1\" 200", "request_id": "3551c431", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T15:26:57.546750Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:58712 - \"GET /health HTTP/1.1\" 200", "request_id": "e277f792", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T15:27:15.284765Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:58721 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "b3f08897", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T15:27:15.334274Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:58721 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "53e39e89", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T15:27:15.430777Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:58721 - \"POST /api/v1/generation/architecture HTTP/1.1\" 200", "request_id": "46b98231", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T15:27:15.452164Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:58721 - \"GET /api/v1/tasks/e1a477c6-278b-4aa2-a532-6dcf9097bc61 HTTP/1.1\" 404", "request_id": "76f2353b", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T15:27:20.466138Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:58732 - \"GET /api/v1/tasks/e1a477c6-278b-4aa2-a532-6dcf9097bc61 HTTP/1.1\" 404", "request_id": "769096d0", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T15:27:25.722828Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:58736 - \"GET /api/v1/tasks/e1a477c6-278b-4aa2-a532-6dcf9097bc61 HTTP/1.1\" 404", "request_id": "b183976f", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T15:27:30.741884Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:58736 - \"GET /api/v1/tasks/e1a477c6-278b-4aa2-a532-6dcf9097bc61 HTTP/1.1\" 404", "request_id": "721cd065", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T15:27:36.020750Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:58738 - \"GET /api/v1/tasks/e1a477c6-278b-4aa2-a532-6dcf9097bc61 HTTP/1.1\" 404", "request_id": "ad6689c3", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T15:27:41.034033Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:58738 - \"GET /api/v1/tasks/e1a477c6-278b-4aa2-a532-6dcf9097bc61 HTTP/1.1\" 404", "request_id": "0dbc19bb", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T15:27:46.046643Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:58738 - \"GET /api/v1/tasks/e1a477c6-278b-4aa2-a532-6dcf9097bc61 HTTP/1.1\" 404", "request_id": "e64c2645", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T15:27:51.047682Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:58738 - \"GET /api/v1/tasks/e1a477c6-278b-4aa2-a532-6dcf9097bc61 HTTP/1.1\" 404", "request_id": "926f832d", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T15:27:56.329433Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:58766 - \"GET /api/v1/tasks/e1a477c6-278b-4aa2-a532-6dcf9097bc61 HTTP/1.1\" 404", "request_id": "a6531ad8", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T15:28:01.340754Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:58766 - \"GET /api/v1/tasks/e1a477c6-278b-4aa2-a532-6dcf9097bc61 HTTP/1.1\" 404", "request_id": "43c0b79a", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "development"}
{"timestamp": "2025-06-19T17:13:48.696000Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:58101 - \"GET /health HTTP/1.1\" 200", "request_id": "08c22d22", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T17:19:31.410192Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:58402 - \"GET /health HTTP/1.1\" 200", "request_id": "74027513", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T17:21:08.988036Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:58551 - \"GET /health HTTP/1.1\" 200", "request_id": "9342c8d1", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T17:21:09.289485Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:58553 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "62ad49a9", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T17:21:09.342686Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:58553 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "e185c572", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T17:21:09.357045Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:58553 - \"POST /api/v1/generation/architecture HTTP/1.1\" 429", "request_id": "c5be89ce", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T17:31:04.568014Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59087 - \"GET /health HTTP/1.1\" 200", "request_id": "7836c11e", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T17:31:15.563861Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59089 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "7f93d3d1", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T17:31:15.589633Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59089 - \"GET /api/v1/novels/?limit=100 HTTP/1.1\" 200", "request_id": "57d33e0e", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T17:31:15.649251Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59089 - \"DELETE /api/v1/novels/10 HTTP/1.1\" 200", "request_id": "9a5d0961", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T17:31:15.673666Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59089 - \"DELETE /api/v1/novels/9 HTTP/1.1\" 200", "request_id": "9d27157a", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T17:31:15.697466Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59089 - \"DELETE /api/v1/novels/8 HTTP/1.1\" 200", "request_id": "3c496f5b", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T17:31:15.723473Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59089 - \"DELETE /api/v1/novels/7 HTTP/1.1\" 200", "request_id": "43daf467", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T17:31:15.749471Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59089 - \"DELETE /api/v1/novels/6 HTTP/1.1\" 200", "request_id": "db95a5ff", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T17:31:15.773341Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59089 - \"DELETE /api/v1/novels/5 HTTP/1.1\" 200", "request_id": "354294f0", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T17:31:15.796806Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59089 - \"DELETE /api/v1/novels/4 HTTP/1.1\" 200", "request_id": "892942b4", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T17:31:15.821815Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59089 - \"DELETE /api/v1/novels/3 HTTP/1.1\" 200", "request_id": "889d1da9", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T17:31:15.845816Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59089 - \"DELETE /api/v1/novels/2 HTTP/1.1\" 200", "request_id": "cc0d9bcb", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T17:31:15.871837Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59089 - \"DELETE /api/v1/novels/1 HTTP/1.1\" 200", "request_id": "6f898f61", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T17:31:15.878843Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59089 - \"GET /api/v1/auth/me HTTP/1.1\" 200", "request_id": "68825476", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T17:31:15.886843Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59089 - \"GET /api/v1/users/quota HTTP/1.1\" 200", "request_id": "5c5f298b", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T17:31:22.367345Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59094 - \"GET /health HTTP/1.1\" 200", "request_id": "daf49ff2", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T17:31:25.005446Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59096 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "d6c80f82", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T17:31:25.045244Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59096 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "b1612117", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T17:31:25.154312Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59096 - \"POST /api/v1/generation/architecture HTTP/1.1\" 200", "request_id": "a491634e", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T17:31:25.168313Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59096 - \"GET /api/v1/tasks/3d77b679-cebc-441c-b5ba-131ee91fecef HTTP/1.1\" 500", "request_id": "7a28202e", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T17:31:30.172948Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59110 - \"GET /api/v1/tasks/3d77b679-cebc-441c-b5ba-131ee91fecef HTTP/1.1\" 500", "request_id": "e3e59ccc", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T17:31:35.187556Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59110 - \"GET /api/v1/tasks/3d77b679-cebc-441c-b5ba-131ee91fecef HTTP/1.1\" 500", "request_id": "8d4ca3bd", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:08:06.114503Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:49533 - \"GET /health HTTP/1.1\" 200", "request_id": "03808d00", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:08:57.045498Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:49578 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "5e4f30c2", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:08:57.074110Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:49578 - \"POST /api/v1/novels/ HTTP/1.1\" 500", "request_id": "0c80afc9", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:14:59.740272Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:50304 - \"GET /health HTTP/1.1\" 200", "request_id": "a416a4fe", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:15:01.650509Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:50308 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "7a5e7811", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:15:01.677323Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:50308 - \"POST /api/v1/novels/ HTTP/1.1\" 500", "request_id": "27bbcd4f", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:15:31.261579Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:50414 - \"GET /health HTTP/1.1\" 200", "request_id": "0917c866", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:15:34.465170Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:50416 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "596711e7", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:15:34.492678Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:50416 - \"POST /api/v1/novels/ HTTP/1.1\" 500", "request_id": "7348ca9f", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:16:32.740286Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:50499 - \"GET /health HTTP/1.1\" 200", "request_id": "e2c642f7", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:16:35.242033Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:50505 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "922e2537", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:16:35.285515Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:50505 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "98e3f1ae", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:16:35.396525Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:50505 - \"POST /api/v1/generation/architecture HTTP/1.1\" 503", "request_id": "28894617", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:19:47.788535Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:50887 - \"GET /health HTTP/1.1\" 200", "request_id": "1acc2919", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:19:51.693954Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:50903 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "cdb2463e", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:19:52.009255Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:50903 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "0961cce4", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:19:52.110791Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:50903 - \"POST /api/v1/generation/architecture HTTP/1.1\" 503", "request_id": "d8b635e3", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:20:49.252145Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:51156 - \"GET /health HTTP/1.1\" 200", "request_id": "bb23d3e6", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:20:51.025901Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:51162 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "9c818475", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:20:51.067558Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:51162 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "87069901", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:20:51.182531Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:51162 - \"POST /api/v1/generation/architecture HTTP/1.1\" 200", "request_id": "6d6e89dd", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:20:51.194040Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:51162 - \"GET /api/v1/tasks/c1a2f59a-12b2-4d68-8c91-23f07f0e250f HTTP/1.1\" 200", "request_id": "cfe3fc30", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:20:56.210703Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:51162 - \"GET /api/v1/tasks/c1a2f59a-12b2-4d68-8c91-23f07f0e250f HTTP/1.1\" 200", "request_id": "2b673b83", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:21:01.510452Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:51184 - \"GET /api/v1/tasks/c1a2f59a-12b2-4d68-8c91-23f07f0e250f HTTP/1.1\" 200", "request_id": "862c2b4e", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:21:06.512431Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:51189 - \"GET /api/v1/tasks/c1a2f59a-12b2-4d68-8c91-23f07f0e250f HTTP/1.1\" 200", "request_id": "5bd0980f", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:21:11.789682Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:51199 - \"GET /api/v1/tasks/c1a2f59a-12b2-4d68-8c91-23f07f0e250f HTTP/1.1\" 200", "request_id": "252e02d7", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:21:16.802939Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:51199 - \"GET /api/v1/tasks/c1a2f59a-12b2-4d68-8c91-23f07f0e250f HTTP/1.1\" 200", "request_id": "3a5aedb4", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:21:22.093538Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:51222 - \"GET /api/v1/tasks/c1a2f59a-12b2-4d68-8c91-23f07f0e250f HTTP/1.1\" 200", "request_id": "e38506b8", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:21:27.133014Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:51223 - \"GET /api/v1/tasks/c1a2f59a-12b2-4d68-8c91-23f07f0e250f HTTP/1.1\" 200", "request_id": "40a88c36", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:21:32.143244Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:51223 - \"GET /api/v1/tasks/c1a2f59a-12b2-4d68-8c91-23f07f0e250f HTTP/1.1\" 200", "request_id": "6b8d49fc", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:21:37.430844Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:51243 - \"GET /api/v1/tasks/c1a2f59a-12b2-4d68-8c91-23f07f0e250f HTTP/1.1\" 200", "request_id": "2fcd9452", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:21:42.443005Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:51243 - \"GET /api/v1/tasks/c1a2f59a-12b2-4d68-8c91-23f07f0e250f HTTP/1.1\" 200", "request_id": "2b115455", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:21:47.459399Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:51243 - \"GET /api/v1/tasks/c1a2f59a-12b2-4d68-8c91-23f07f0e250f HTTP/1.1\" 200", "request_id": "25a8c425", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:21:52.726140Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:51266 - \"GET /api/v1/tasks/c1a2f59a-12b2-4d68-8c91-23f07f0e250f HTTP/1.1\" 200", "request_id": "ec010e38", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:25:12.018099Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:51572 - \"GET /health HTTP/1.1\" 200", "request_id": "71119502", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:25:14.373480Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:51574 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "7ba173f3", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:25:14.415844Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:51574 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "53c98a7c", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:25:14.525554Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:51574 - \"POST /api/v1/generation/architecture HTTP/1.1\" 200", "request_id": "00e4ba5c", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:25:14.535870Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:51574 - \"GET /api/v1/tasks/e46b9d5d-3f35-483e-9da3-63e77fc96649 HTTP/1.1\" 200", "request_id": "26adfe68", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:25:19.562959Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:51580 - \"GET /api/v1/tasks/e46b9d5d-3f35-483e-9da3-63e77fc96649 HTTP/1.1\" 200", "request_id": "e47a280d", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:25:24.577785Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:51580 - \"GET /api/v1/tasks/e46b9d5d-3f35-483e-9da3-63e77fc96649 HTTP/1.1\" 200", "request_id": "bc564344", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:25:29.601155Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:51580 - \"GET /api/v1/tasks/e46b9d5d-3f35-483e-9da3-63e77fc96649 HTTP/1.1\" 200", "request_id": "a7d4b484", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:25:34.911760Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:51669 - \"GET /api/v1/tasks/e46b9d5d-3f35-483e-9da3-63e77fc96649 HTTP/1.1\" 200", "request_id": "c78e39d7", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:25:39.922932Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:51669 - \"GET /api/v1/tasks/e46b9d5d-3f35-483e-9da3-63e77fc96649 HTTP/1.1\" 200", "request_id": "c0b53480", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:25:45.231002Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:51686 - \"GET /api/v1/tasks/e46b9d5d-3f35-483e-9da3-63e77fc96649 HTTP/1.1\" 200", "request_id": "1ae4aa7a", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:25:50.236636Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:51687 - \"GET /api/v1/tasks/e46b9d5d-3f35-483e-9da3-63e77fc96649 HTTP/1.1\" 200", "request_id": "2e0d0eb5", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:33:21.958944Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:52459 - \"GET /health HTTP/1.1\" 200", "request_id": "70e08687", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:33:29.189936Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:52473 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "0ee60d18", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:33:29.230936Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:52473 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "6568497c", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:33:29.402980Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:52473 - \"POST /api/v1/generation/architecture HTTP/1.1\" 200", "request_id": "2f8d576d", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:33:29.415981Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:52473 - \"GET /api/v1/tasks/ecd1d6ff-8f0c-4221-b0b7-b84808636923 HTTP/1.1\" 200", "request_id": "2ed80971", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:33:34.431839Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:52504 - \"GET /api/v1/tasks/ecd1d6ff-8f0c-4221-b0b7-b84808636923 HTTP/1.1\" 200", "request_id": "03e96908", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:33:39.442423Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:52504 - \"GET /api/v1/tasks/ecd1d6ff-8f0c-4221-b0b7-b84808636923 HTTP/1.1\" 200", "request_id": "1703b8d3", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:33:44.743348Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:52515 - \"GET /api/v1/tasks/ecd1d6ff-8f0c-4221-b0b7-b84808636923 HTTP/1.1\" 200", "request_id": "15225c7c", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:33:49.758988Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:52516 - \"GET /api/v1/tasks/ecd1d6ff-8f0c-4221-b0b7-b84808636923 HTTP/1.1\" 200", "request_id": "3b7b60d0", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:33:54.773618Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:52516 - \"GET /api/v1/tasks/ecd1d6ff-8f0c-4221-b0b7-b84808636923 HTTP/1.1\" 200", "request_id": "33175c76", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:34:00.057836Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:52519 - \"GET /api/v1/tasks/ecd1d6ff-8f0c-4221-b0b7-b84808636923 HTTP/1.1\" 200", "request_id": "a885cd47", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:34:05.061270Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:52520 - \"GET /api/v1/tasks/ecd1d6ff-8f0c-4221-b0b7-b84808636923 HTTP/1.1\" 200", "request_id": "691cc82a", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:34:10.374532Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:52524 - \"GET /api/v1/tasks/ecd1d6ff-8f0c-4221-b0b7-b84808636923 HTTP/1.1\" 200", "request_id": "6de761ee", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:34:15.410596Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:52537 - \"GET /api/v1/tasks/ecd1d6ff-8f0c-4221-b0b7-b84808636923 HTTP/1.1\" 200", "request_id": "e2f8f3f6", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:34:20.698122Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:52551 - \"GET /api/v1/tasks/ecd1d6ff-8f0c-4221-b0b7-b84808636923 HTTP/1.1\" 200", "request_id": "1558f4db", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:34:25.719232Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:52572 - \"GET /api/v1/tasks/ecd1d6ff-8f0c-4221-b0b7-b84808636923 HTTP/1.1\" 200", "request_id": "1ec3ca43", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:34:31.021761Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:52585 - \"GET /api/v1/tasks/ecd1d6ff-8f0c-4221-b0b7-b84808636923 HTTP/1.1\" 200", "request_id": "4a7ee4bc", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:34:36.037640Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:52585 - \"GET /api/v1/tasks/ecd1d6ff-8f0c-4221-b0b7-b84808636923 HTTP/1.1\" 200", "request_id": "9542c44a", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:34:41.296218Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:52597 - \"GET /api/v1/tasks/ecd1d6ff-8f0c-4221-b0b7-b84808636923 HTTP/1.1\" 200", "request_id": "76e7db53", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:34:46.315048Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:52600 - \"GET /api/v1/tasks/ecd1d6ff-8f0c-4221-b0b7-b84808636923 HTTP/1.1\" 200", "request_id": "9ad1471f", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:34:51.335674Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:52600 - \"GET /api/v1/tasks/ecd1d6ff-8f0c-4221-b0b7-b84808636923 HTTP/1.1\" 200", "request_id": "ffe449d4", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:43:56.737199Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:53574 - \"GET /health HTTP/1.1\" 200", "request_id": "6201ce89", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:44:00.190605Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:53576 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "a7309841", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:44:00.231042Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:53576 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "3593a529", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:44:00.337867Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:53576 - \"POST /api/v1/generation/architecture HTTP/1.1\" 200", "request_id": "6251dc0e", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:44:00.348167Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:53576 - \"GET /api/v1/tasks/c3e58a37-d437-45e6-a43f-ce91bd064b05 HTTP/1.1\" 200", "request_id": "102ce676", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:44:05.390097Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:53586 - \"GET /api/v1/tasks/c3e58a37-d437-45e6-a43f-ce91bd064b05 HTTP/1.1\" 200", "request_id": "c4701522", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:44:10.405498Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:53586 - \"GET /api/v1/tasks/c3e58a37-d437-45e6-a43f-ce91bd064b05 HTTP/1.1\" 200", "request_id": "d64fac59", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:44:15.686016Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:53596 - \"GET /api/v1/tasks/c3e58a37-d437-45e6-a43f-ce91bd064b05 HTTP/1.1\" 200", "request_id": "22c73273", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:56:00.310515Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:54723 - \"GET /health HTTP/1.1\" 200", "request_id": "3e48a295", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:56:02.505653Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:54727 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "fbb3a4d9", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:56:02.544653Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:54727 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "fae6acf2", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:56:02.649707Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:54727 - \"POST /api/v1/generation/architecture HTTP/1.1\" 200", "request_id": "3524a5d4", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:56:02.660783Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:54727 - \"GET /api/v1/tasks/ddb63fdd-bb02-4c5d-bea6-62ca1ff3f919 HTTP/1.1\" 200", "request_id": "22783978", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:56:07.677785Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:54741 - \"GET /api/v1/tasks/ddb63fdd-bb02-4c5d-bea6-62ca1ff3f919 HTTP/1.1\" 200", "request_id": "2433280c", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:56:12.962361Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:54743 - \"GET /api/v1/tasks/ddb63fdd-bb02-4c5d-bea6-62ca1ff3f919 HTTP/1.1\" 200", "request_id": "548286d7", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:56:17.985243Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:54746 - \"GET /api/v1/tasks/ddb63fdd-bb02-4c5d-bea6-62ca1ff3f919 HTTP/1.1\" 200", "request_id": "afd238c3", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:56:23.266307Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:55161 - \"GET /api/v1/tasks/ddb63fdd-bb02-4c5d-bea6-62ca1ff3f919 HTTP/1.1\" 200", "request_id": "6ef9cd23", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:56:28.278833Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:55176 - \"GET /api/v1/tasks/ddb63fdd-bb02-4c5d-bea6-62ca1ff3f919 HTTP/1.1\" 200", "request_id": "5fb081c0", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:57:47.496637Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:55539 - \"GET /health HTTP/1.1\" 200", "request_id": "82fe9c9c", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:57:52.373099Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:55549 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "00fe9beb", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:57:52.415201Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:55549 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "b7ccf1dc", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:57:52.528928Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:55549 - \"POST /api/v1/generation/architecture HTTP/1.1\" 200", "request_id": "138bd03b", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:57:52.539746Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:55549 - \"GET /api/v1/tasks/c30b0afd-95b9-4d2f-9aa4-5be927f249f9 HTTP/1.1\" 200", "request_id": "4885ed33", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T19:57:57.548221Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:55674 - \"GET /api/v1/tasks/c30b0afd-95b9-4d2f-9aa4-5be927f249f9 HTTP/1.1\" 200", "request_id": "acdb13f8", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:04:40.898366Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:56664 - \"GET /health HTTP/1.1\" 200", "request_id": "6a67f27b", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:04:44.560710Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:56666 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "473b546c", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:04:44.605804Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:56666 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "0445cb91", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:04:44.731783Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:56666 - \"POST /api/v1/generation/architecture HTTP/1.1\" 200", "request_id": "cf37d187", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:04:44.741781Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:56666 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "10e57d12", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:04:49.762089Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:56682 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "d40a9dd9", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:04:55.044251Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:56684 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "7b491ba0", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:05:00.088115Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:56687 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "576c7ed4", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:05:05.089922Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:56687 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "8ed80602", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:05:10.381737Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:56696 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "cf516d4c", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:05:15.400857Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:56699 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "73144c4f", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:05:20.673610Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:56723 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "c81ea06d", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:05:25.694617Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:56723 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "bea75feb", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:05:30.981473Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:56789 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "7c76211d", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:05:35.983648Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:56802 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "e3dd65da", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:05:41.258704Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:56823 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "6fa00281", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:05:46.277662Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:56828 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "cfa45895", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:05:51.557976Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:56832 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "c623d340", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:05:56.582262Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:56837 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "d7f8076b", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:06:01.598905Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:56837 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "bbc22ae6", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:06:06.886258Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:56851 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "121d20ea", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:06:11.886151Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:56865 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "4c73bebb", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:06:17.162909Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:56873 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "34c0df38", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:06:22.166326Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:56880 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "409b196d", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:06:27.455487Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:56884 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "767cbefe", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:06:32.471332Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:56884 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "8244c3ec", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:06:37.765279Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:56906 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "4efafd0d", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:06:42.787599Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:56911 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "eee11b9d", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:06:48.051057Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:56922 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "2c26fd1b", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:06:53.068230Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:56922 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "1c27509c", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:06:58.336377Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:56930 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "9c9e5a6f", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:07:03.350906Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:56930 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "c3880e42", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:07:08.635230Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:56932 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "dc1ac9b4", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:07:13.659858Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:56933 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "5af9f051", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:07:18.922524Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:56940 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "56914a31", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:07:23.942217Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:56940 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "3700992b", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:07:29.229931Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:56954 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "3bea87c9", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:07:34.240361Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:56963 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "e288abdd", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:07:39.498267Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:56997 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "f07c3d52", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:07:44.521343Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:57010 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "e05cc402", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:07:49.794537Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:57023 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "d03b6dfc", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:07:54.790402Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:57028 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "411e64ce", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:08:00.073089Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:57036 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "cf08866a", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:08:05.078973Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:57037 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "fba6dc11", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:08:10.359955Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:57039 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "bec9f25d", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:08:15.371562Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:57040 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "cbb1f9fa", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:08:20.620475Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:57042 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "b8956693", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:08:25.634287Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:57062 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "685035ef", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:08:30.908037Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:57074 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "32625ce6", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:08:35.931992Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:57081 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "132eb92d", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:08:41.191061Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:57083 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "3605be44", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:08:46.203590Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:57083 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "a1d04d90", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:08:51.462188Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:57085 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "4fac1b06", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:08:56.486443Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:57087 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "58fbe988", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:09:01.765133Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:57091 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "f4d3ab97", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:09:06.791302Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:57092 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "a3e9f4bb", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:09:12.065219Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:57094 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "91be8145", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:09:17.095386Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:57095 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "19c3465c", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:09:22.371242Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:57101 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "4b4b3828", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:09:27.360648Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:57102 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "3f17c3a0", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:09:32.678212Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:57119 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "10363b6b", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:09:37.703964Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:57139 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "4617bc43", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:09:42.964035Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:57141 - \"GET /api/v1/tasks/f04e4c33-22e2-461d-9d5d-27344ebfe38e HTTP/1.1\" 200", "request_id": "c7926ae8", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:20:27.679688Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:58409 - \"POST /api/v1/auth/wechat/login HTTP/1.1\" 401", "request_id": "93d88e6c", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:22:26.051206Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:58692 - \"POST /api/v1/auth/wechat/login HTTP/1.1\" 401", "request_id": "011211e2", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:22:48.637975Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:58817 - \"GET /health HTTP/1.1\" 200", "request_id": "efa83cb0", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:23:36.050194Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:58906 - \"GET /health HTTP/1.1\" 200", "request_id": "d6195118", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:23:39.046298Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:58908 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "c7c4ff0a", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:23:39.087808Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:58908 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "7efa7f38", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:23:39.098318Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:58908 - \"POST /api/v1/generation/architecture HTTP/1.1\" 429", "request_id": "f87831da", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:25:05.850069Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59029 - \"GET /health HTTP/1.1\" 200", "request_id": "102c689f", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:25:09.656000Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59031 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "44270b2d", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:25:09.669073Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59031 - \"GET /api/v1/novels/?limit=100 HTTP/1.1\" 200", "request_id": "f8afef6e", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:25:09.725426Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59031 - \"DELETE /api/v1/novels/9d5c9600-c7f8-4d69-970a-84dd322abe4b HTTP/1.1\" 200", "request_id": "45f8b73e", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:25:09.767046Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59031 - \"DELETE /api/v1/novels/9a408373-c5b5-4531-9afa-a73b72eac1fd HTTP/1.1\" 200", "request_id": "d9bcefa8", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:25:09.801188Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59031 - \"DELETE /api/v1/novels/a045fcc7-0693-4d70-9ce9-58c5efb9b8c9 HTTP/1.1\" 200", "request_id": "5e59a162", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:25:09.835786Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59031 - \"DELETE /api/v1/novels/08d8e584-89b7-4ec7-aebe-59eef4d3fb81 HTTP/1.1\" 200", "request_id": "abecb3ac", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:25:09.867784Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59031 - \"DELETE /api/v1/novels/a1790ae1-1321-408e-b977-079984b50ab0 HTTP/1.1\" 200", "request_id": "92ca4587", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:25:09.900788Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59031 - \"DELETE /api/v1/novels/9afc3499-4873-43f3-8aa0-a58c92c25996 HTTP/1.1\" 200", "request_id": "62f29a47", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:25:09.931167Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59031 - \"DELETE /api/v1/novels/2a6dbbe6-b9eb-4a87-bb7d-4b8b2b129b2f HTTP/1.1\" 200", "request_id": "3c2632a2", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:25:09.959182Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59031 - \"DELETE /api/v1/novels/0cfecc1c-7677-42e0-a3ee-cf212136a6f3 HTTP/1.1\" 200", "request_id": "c5e1b3b8", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:25:09.984255Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59031 - \"DELETE /api/v1/novels/a5a46d39-43f3-4ac6-afb1-1fc40fe62077 HTTP/1.1\" 200", "request_id": "08dfa927", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:25:10.012981Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59031 - \"DELETE /api/v1/novels/4b034326-6904-4617-bdbc-43023f0f69ca HTTP/1.1\" 200", "request_id": "c3f39f77", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:25:10.020234Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59031 - \"GET /api/v1/auth/me HTTP/1.1\" 200", "request_id": "36de56f5", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:25:10.027241Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59031 - \"GET /api/v1/users/quota HTTP/1.1\" 200", "request_id": "5314d6df", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:25:15.717612Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59037 - \"GET /health HTTP/1.1\" 200", "request_id": "c9a4ac0a", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:25:18.822235Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59039 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "9f365df0", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:25:18.862813Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59039 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "f9e366f3", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:25:18.985093Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59039 - \"POST /api/v1/generation/architecture HTTP/1.1\" 200", "request_id": "03b047ad", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:25:18.995092Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59039 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "bea04318", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:25:24.020183Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59102 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "408fb2fd", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:25:29.310154Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59126 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "6eeb8efe", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:25:34.335585Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59126 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "a636e676", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:25:39.606139Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59162 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "8d14719a", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:25:44.624011Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59167 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "f54bcf3d", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:25:49.926692Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59185 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "bfa07506", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:25:54.939801Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59191 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "4fa2d963", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:25:59.952600Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59191 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "8a3f7d9a", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:26:05.238693Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59203 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "2ecfebf7", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:26:10.254584Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59208 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "ffe26471", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:26:15.545906Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59214 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "6a44f1c2", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:26:20.567856Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59214 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "0a0faaaf", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:26:25.844901Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59225 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "b06e1b6f", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:26:30.857283Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59225 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "2c030c26", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:26:36.149130Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59245 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "1ddf8718", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:26:41.154519Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59246 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "4948d529", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:26:46.441878Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59248 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "34fdca3b", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:26:51.469496Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59251 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "d8a05a93", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:26:56.745434Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59253 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "983ec7b8", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:27:01.754696Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59261 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "49440b11", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:27:07.034419Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59263 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "9e039d26", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:27:12.049410Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59264 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "060c4854", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:27:17.321023Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59268 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "c0a65e80", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:27:22.344884Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59273 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "9bbd50a3", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:27:27.359028Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59273 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "fa82d7e6", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:27:32.629966Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59288 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "29e82baa", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:27:37.644210Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59288 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "7954106a", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:27:42.656517Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59288 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "8210303c", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:27:47.669566Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59288 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "8ab3ca5c", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:27:52.919194Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59328 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "d5b58a7f", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:27:57.937760Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59328 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "f2efc283", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:28:03.204173Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59332 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "0776edb4", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:28:08.205669Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59338 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "2eca4c6b", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:28:13.483783Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59340 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "e788f53d", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:28:18.481588Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59341 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "575dfdcd", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:28:23.761959Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59349 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "7e086120", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:28:28.818051Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59352 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "d0194f1a", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:28:34.128532Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59361 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "e1ae3e34", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:28:39.144021Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59361 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "a25b24b6", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:28:44.415268Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59375 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "b07c33d2", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:28:49.425290Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59390 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "36bc17b4", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:28:54.751373Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59402 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "0c9cc2bd", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:28:59.807819Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59410 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "5fd44471", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:29:05.058499Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59417 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "b8f41acf", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:29:10.070456Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59427 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "3b061388", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:29:15.082974Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59427 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "eaf4022d", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:29:20.095568Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59427 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "a2a668cc", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:29:25.383471Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59437 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "f470bd2b", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:29:30.412067Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59448 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "2dcabb69", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:29:35.686509Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59456 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "7f389cb1", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:29:40.709367Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59456 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "a545983d", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:29:45.970042Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59471 - \"GET /api/v1/tasks/f04743df-2d0a-418b-968a-4f6771d89e19 HTTP/1.1\" 200", "request_id": "bca89520", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:30:50.639142Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59660 - \"GET /health HTTP/1.1\" 200", "request_id": "c360141f", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:30:55.284933Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59662 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "858a8720", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:30:55.293440Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59662 - \"GET /api/v1/users/quota HTTP/1.1\" 200", "request_id": "cda266a8", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:30:55.320136Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59662 - \"GET /api/v1/novels/?limit=100 HTTP/1.1\" 200", "request_id": "333a6607", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:30:55.383583Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59662 - \"DELETE /api/v1/novels/15438a3b-abbb-42c0-8ead-85f1b2633ae4 HTTP/1.1\" 200", "request_id": "30e9f71c", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:30:55.390590Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59662 - \"GET /api/v1/auth/me HTTP/1.1\" 200", "request_id": "bb9d03b5", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:30:55.398907Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59662 - \"GET /api/v1/users/quota HTTP/1.1\" 200", "request_id": "5f43ad4b", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:30:59.884037Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59670 - \"GET /health HTTP/1.1\" 200", "request_id": "c59c96a2", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:31:01.659330Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59672 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "3f997a01", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:31:01.703516Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59672 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "e3c28c6e", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:31:01.811251Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59672 - \"POST /api/v1/generation/architecture HTTP/1.1\" 200", "request_id": "27e0c592", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:31:01.822726Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59672 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "2f8c3a1c", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:31:06.839273Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59680 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "64fae98e", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:31:12.132437Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59694 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "bf532c33", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:31:17.157345Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59697 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "20fef113", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:31:22.448601Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59708 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "a9475172", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:31:27.463742Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59708 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "8ca2f8d3", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:31:32.735652Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59726 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "752f8650", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:31:37.758020Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59747 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "2bf299b6", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:31:43.045995Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59769 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "248ba5d0", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:31:48.064215Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59769 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "199f23a5", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:31:53.080355Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59769 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "01507020", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:31:58.093347Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59769 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "d58de24a", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:32:03.396737Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59808 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "13fda132", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:32:08.416755Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59817 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "5bd5e77e", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:32:13.687973Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59831 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "bcec7d57", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:32:18.711087Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59844 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "226fbf07", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:32:23.967415Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59862 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "a9039a38", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:32:28.989374Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59862 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "a6eac71b", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:32:34.248777Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59892 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "74ad66b0", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:32:39.286989Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59913 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "81ff10bb", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:32:44.573984Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59929 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "c3387f89", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:32:49.596550Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59934 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "a2280912", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:32:54.868818Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59940 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "46259057", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:32:59.865377Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59948 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "35d32d21", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:33:05.139202Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59950 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "006f85bd", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:33:10.150059Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59950 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "a2027e62", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:33:15.434941Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59952 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "d5a3d873", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:33:20.457947Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59952 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "54cbef95", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:33:25.715060Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59964 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "023b290a", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:33:30.735375Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59989 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "dac9270f", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:33:36.020017Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:60012 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "55035246", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:33:41.026309Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:60017 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "b0cd0362", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:33:46.295034Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:60019 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "4d3b980d", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:33:51.317935Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:60019 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "07b8c469", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:33:56.331689Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:60019 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "25ca234e", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:34:01.596482Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:60061 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "3af60d6d", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:34:06.607199Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:60081 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "31d870ec", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:34:11.868115Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:60093 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "a015028a", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:34:16.855817Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:60102 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "8ba016dd", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:34:22.136249Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:60130 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "ab5774b9", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:34:27.141471Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:60130 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "ebce8792", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:34:32.417440Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:60152 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "234ccb25", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:34:37.403042Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:60163 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "9738afb5", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:34:42.684958Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:60172 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "1634f7f8", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:34:47.704578Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:60177 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "9d8db0a0", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:34:52.983202Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:60181 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "da6ffb44", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:34:57.987125Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:60186 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "954ae288", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:35:03.251854Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:60212 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "ef6fde75", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:35:08.260338Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:60213 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "50ba3202", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:35:13.510335Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:60238 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "81525ac2", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:35:18.522010Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:60245 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "b57e3e99", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:35:23.794360Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:60299 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "fc81fb0b", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:35:28.809649Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:60314 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "9a72a2fd", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:35:34.070287Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:60334 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "ae20d61f", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:35:39.073234Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:60345 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "ce532acd", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:35:44.356810Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:60358 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "c7116b6b", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:35:49.416564Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:60371 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "8313284d", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:35:54.707363Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:60378 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "15638419", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:35:59.734131Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:60382 - \"GET /api/v1/tasks/856afd7a-22fb-424d-929b-e0d0e0d7acae HTTP/1.1\" 200", "request_id": "f606e7f4", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:38:47.206386Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:60685 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "465a1555", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:38:49.293995Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:60688 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "ed29d941", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:38:51.406541Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:60690 - \"POST /api/v1/generation/architecture HTTP/1.1\" 200", "request_id": "fd52b737", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:43:46.517762Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:61329 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "dfc62847", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:43:48.632662Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:61332 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "21336e42", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:45:17.808378Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:61445 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "da9ec857", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:45:19.897724Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:61447 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "7c47aa1d", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:45:22.036436Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:61495 - \"POST /api/v1/generation/architecture HTTP/1.1\" 503", "request_id": "5ae9b7d2", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:48:59.836640Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:61891 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "57ac2f99", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:49:01.919311Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:61897 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "e3ff01a0", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:49:04.052550Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:61903 - \"POST /api/v1/generation/architecture HTTP/1.1\" 200", "request_id": "bb5c74ce", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:56:24.640950Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62669 - \"GET /health HTTP/1.1\" 200", "request_id": "494ce07d", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:56:26.151540Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62671 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "02432a1a", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:56:26.191647Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62671 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "6e00b680", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:56:26.308923Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62671 - \"POST /api/v1/generation/architecture HTTP/1.1\" 200", "request_id": "af95d341", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:56:26.318923Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62671 - \"GET /api/v1/tasks/15ad44e9-5f47-4efa-9708-9b4cd313cef2 HTTP/1.1\" 200", "request_id": "a85a42d5", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:56:31.346876Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62693 - \"GET /api/v1/tasks/15ad44e9-5f47-4efa-9708-9b4cd313cef2 HTTP/1.1\" 500", "request_id": "ad8b69fd", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:56:36.366528Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62693 - \"GET /api/v1/tasks/15ad44e9-5f47-4efa-9708-9b4cd313cef2 HTTP/1.1\" 500", "request_id": "fb6585fe", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:56:41.621199Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62701 - \"GET /api/v1/tasks/15ad44e9-5f47-4efa-9708-9b4cd313cef2 HTTP/1.1\" 500", "request_id": "d3cfec46", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:56:46.611478Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62708 - \"GET /api/v1/tasks/15ad44e9-5f47-4efa-9708-9b4cd313cef2 HTTP/1.1\" 500", "request_id": "163b6650", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:56:51.906399Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62720 - \"GET /api/v1/tasks/15ad44e9-5f47-4efa-9708-9b4cd313cef2 HTTP/1.1\" 500", "request_id": "e553d1c6", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:56:56.919691Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62727 - \"GET /api/v1/tasks/15ad44e9-5f47-4efa-9708-9b4cd313cef2 HTTP/1.1\" 500", "request_id": "d0af24ec", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:57:02.178228Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62756 - \"GET /api/v1/tasks/15ad44e9-5f47-4efa-9708-9b4cd313cef2 HTTP/1.1\" 500", "request_id": "93acd4c6", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:57:07.203939Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62765 - \"GET /api/v1/tasks/15ad44e9-5f47-4efa-9708-9b4cd313cef2 HTTP/1.1\" 500", "request_id": "e0a58326", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:57:12.482800Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62769 - \"GET /api/v1/tasks/15ad44e9-5f47-4efa-9708-9b4cd313cef2 HTTP/1.1\" 500", "request_id": "f23ab277", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:57:17.499926Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62774 - \"GET /api/v1/tasks/15ad44e9-5f47-4efa-9708-9b4cd313cef2 HTTP/1.1\" 500", "request_id": "e1214e54", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:58:43.601983Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62920 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "0b639263", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:58:45.690824Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62926 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "35974c86", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T20:58:47.846870Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62934 - \"POST /api/v1/generation/architecture HTTP/1.1\" 200", "request_id": "8faae71d", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:00:07.433991Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63098 - \"GET /health HTTP/1.1\" 200", "request_id": "0012c225", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:00:09.619910Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63104 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "95439e90", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:00:09.658330Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63104 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "970d91bf", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:00:09.769329Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63104 - \"POST /api/v1/generation/architecture HTTP/1.1\" 200", "request_id": "0aed22a0", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:00:09.779325Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63104 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "08c1038c", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:00:14.779209Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63113 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "26719bc2", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:00:20.048919Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63121 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "55910a0b", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:00:25.069638Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63121 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "32e5e413", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:00:30.353867Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63219 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "279bb196", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:00:35.372884Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63236 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "8fdc910b", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:00:40.637255Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63253 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "f98eb530", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:00:45.635951Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63259 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "3aaa5683", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:00:50.903447Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63267 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "c6593d47", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:00:55.908383Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63272 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "f67826c8", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:01:01.179995Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63276 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "a55da447", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:01:06.189135Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63276 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "df7a0599", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:01:11.475920Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63280 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "36e2ed1a", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:01:16.492815Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63283 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "6b95ca7c", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:01:21.504400Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63283 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "d81019dd", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:01:26.767307Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63294 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "c465d9dd", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:01:31.794935Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63305 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "bb7c9212", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:01:37.059785Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63323 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "78f1dafe", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:01:42.078125Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63324 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "66be1279", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:01:47.354642Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63326 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "be53b4ad", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:01:52.365843Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63326 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "3ec32c4a", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:01:57.651052Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63331 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "740a7796", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:02:02.656265Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63332 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "d379ea3a", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:02:07.934044Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63334 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "b92fb09b", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:02:12.946523Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63341 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "8c8419d7", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:02:18.198553Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63343 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "cebc8adc", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:02:23.218644Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63351 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "88059e74", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:02:28.492127Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63360 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "4fdd6253", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:02:33.493117Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63369 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "101ff2b5", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:02:38.760078Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63393 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "cf770ac6", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:02:43.778323Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63404 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "d6aaf08a", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:02:48.783673Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63404 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "d0cbe2bc", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:02:54.066717Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63410 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "04b9d5df", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:02:59.076915Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63416 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "6e1dabca", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:03:04.354751Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63418 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "4eb0db58", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:03:09.379037Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63419 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "9ecd5c00", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:03:14.664704Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63421 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "050f9e32", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:03:19.652637Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63422 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "d01c1064", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:03:24.942211Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63428 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "34f46146", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:03:29.957548Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63428 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "057b3a45", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:03:35.224866Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63453 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "4f53eeb8", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:03:40.286316Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63456 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "8a5ae73a", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:03:45.583166Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63459 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "9d9a374e", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:03:50.592557Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63459 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "35211332", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:03:55.610213Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63459 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "5e9cee2c", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:04:00.932473Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63467 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "27b51efd", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:04:05.950671Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63472 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "166f9942", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:04:11.228587Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63477 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "eba30fd5", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:04:16.238181Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63485 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "d2f47db5", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:04:21.519137Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63503 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "757e0f90", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:04:26.586938Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63516 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "5bca9d73", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:04:31.873771Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63529 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "e63a9919", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:04:36.886380Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63539 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "4621a586", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:04:42.174210Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63541 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "bdc4332e", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:04:47.189306Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63548 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "b17fa80d", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:04:52.469662Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63550 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "6e6850a3", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:04:57.480105Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63550 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "2d91c1c0", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:05:02.750145Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63554 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "17eae54d", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:05:07.764209Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63563 - \"GET /api/v1/tasks/f940e2ec-5ed5-481a-95b8-ee43241d4ed3 HTTP/1.1\" 200", "request_id": "78721dab", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:08:12.637601Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63829 - \"GET /health HTTP/1.1\" 200", "request_id": "60030ce2", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:09:04.666491Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63997 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "1aaf7e7b", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:09:04.707093Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63997 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "792cae12", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:09:04.838216Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63997 - \"POST /api/v1/generation/architecture HTTP/1.1\" 200", "request_id": "4ca712c6", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:09:04.847214Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63997 - \"GET /api/v1/tasks/8b36b213-7663-467f-9e2e-bdf619826d84 HTTP/1.1\" 200", "request_id": "0679cdaa", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:09:09.873369Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:64002 - \"GET /api/v1/tasks/8b36b213-7663-467f-9e2e-bdf619826d84 HTTP/1.1\" 200", "request_id": "165e3de8", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:09:15.161313Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:64004 - \"GET /api/v1/tasks/8b36b213-7663-467f-9e2e-bdf619826d84 HTTP/1.1\" 200", "request_id": "d0db8ac1", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:09:20.175524Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:64066 - \"GET /api/v1/tasks/8b36b213-7663-467f-9e2e-bdf619826d84 HTTP/1.1\" 200", "request_id": "50dac381", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:09:25.190014Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:64066 - \"GET /api/v1/tasks/8b36b213-7663-467f-9e2e-bdf619826d84 HTTP/1.1\" 200", "request_id": "eace4c06", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:09:30.250047Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:64066 - \"GET /api/v1/tasks/8b36b213-7663-467f-9e2e-bdf619826d84 HTTP/1.1\" 200", "request_id": "c52263f4", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:09:35.561145Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:64102 - \"GET /api/v1/tasks/8b36b213-7663-467f-9e2e-bdf619826d84 HTTP/1.1\" 200", "request_id": "b34c9339", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-19T21:09:40.575085Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:64102 - \"GET /api/v1/tasks/8b36b213-7663-467f-9e2e-bdf619826d84 HTTP/1.1\" 200", "request_id": "94cef4b7", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T05:52:18.373330Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59899 - \"GET /health HTTP/1.1\" 200", "request_id": "70d77513", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T05:52:18.680277Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59903 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "cca903ee", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T05:52:18.735277Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:59903 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "73347339", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T05:54:18.710203Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:60041 - \"GET /health HTTP/1.1\" 200", "request_id": "35aa4496", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T05:54:18.994587Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:60045 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "f537fcca", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T05:54:19.043587Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:60045 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "2724cafc", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T05:59:10.745220Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:60417 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "f550f00b", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T05:59:10.799665Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:60417 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "5c28fdb9", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T06:00:05.471989Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:60456 - \"GET / HTTP/1.1\" 200", "request_id": "e70099b4", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T06:00:05.477993Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:60456 - \"GET /health HTTP/1.1\" 200", "request_id": "3387bcaf", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T06:00:05.489993Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:60456 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "1ceee46c", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T06:02:08.710213Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:60560 - \"GET / HTTP/1.1\" 200", "request_id": "91b56158", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T06:02:33.772273Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:60594 - \"GET / HTTP/1.1\" 200", "request_id": "072c41a0", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T06:11:18.534113Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:61432 - \"GET /health HTTP/1.1\" 200", "request_id": "79706c0f", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T06:11:18.838263Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:61434 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "aafe2316", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T06:11:18.889262Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:61434 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "0b3f49ab", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T06:11:19.130324Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:61434 - \"POST /api/v1/generation/architecture HTTP/1.1\" 200", "request_id": "90dfcb27", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T06:15:26.408177Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:61776 - \"GET /health HTTP/1.1\" 200", "request_id": "306319f0", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T06:15:26.710716Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:61778 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "d5199433", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T06:15:26.759715Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:61778 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "fbe7cabd", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T06:15:26.984058Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:61778 - \"POST /api/v1/generation/architecture HTTP/1.1\" 200", "request_id": "884acb5e", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T06:18:10.282377Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:61906 - \"GET /health HTTP/1.1\" 200", "request_id": "030dc98d", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T06:18:12.533367Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:61916 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "053dd89b", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T06:18:12.547367Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:61916 - \"GET /api/v1/users/quota HTTP/1.1\" 200", "request_id": "e2cf02c6", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T06:18:12.580894Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:61916 - \"GET /api/v1/novels/?limit=100 HTTP/1.1\" 200", "request_id": "71a230b2", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T06:18:12.657162Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:61916 - \"DELETE /api/v1/novels/aed71d3e-209b-4c93-a990-92ab8df43223 HTTP/1.1\" 200", "request_id": "9b5c919e", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T06:18:12.690172Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:61916 - \"DELETE /api/v1/novels/82273452-320b-4334-8054-63a2c11cf5ed HTTP/1.1\" 200", "request_id": "9e91ddff", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T06:18:12.722081Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:61916 - \"DELETE /api/v1/novels/e234bb2a-c352-4f42-868c-09141ac10c52 HTTP/1.1\" 200", "request_id": "f1edbb12", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T06:19:14.061919Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62005 - \"GET /health HTTP/1.1\" 200", "request_id": "8d9fc9d0", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T06:19:14.349934Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62007 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "6b7d311a", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T06:19:14.401447Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62007 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "b40112f7", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T06:19:14.600778Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62007 - \"POST /api/v1/generation/architecture HTTP/1.1\" 200", "request_id": "f0eae940", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T06:22:09.435897Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62183 - \"GET /health HTTP/1.1\" 200", "request_id": "c77eedaf", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T06:22:09.722474Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62185 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "2ae59ad1", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T06:22:09.770655Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62185 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "5936ea3c", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T06:22:09.972772Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62185 - \"POST /api/v1/generation/architecture HTTP/1.1\" 200", "request_id": "ea216289", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T06:23:29.953059Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62293 - \"GET /health HTTP/1.1\" 200", "request_id": "b68d68dc", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T06:23:30.239245Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62295 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "d21206a0", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T06:23:30.288598Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62295 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "8a60545f", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T06:23:30.467485Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:62295 - \"POST /api/v1/generation/architecture HTTP/1.1\" 200", "request_id": "509906a2", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T08:29:30.942068Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:50490 - \"POST /api/v1/auth/login HTTP/1.1\" 404", "request_id": "2b856a85", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T08:29:49.241002Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:50502 - \"GET /health HTTP/1.1\" 200", "request_id": "0ec2058c", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T08:29:51.814338Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:50504 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "6f2fb7f6", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T08:29:51.822129Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:50504 - \"GET /api/v1/users/quota HTTP/1.1\" 200", "request_id": "389aa60f", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T08:29:51.859643Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:50504 - \"GET /api/v1/novels/?limit=100 HTTP/1.1\" 200", "request_id": "6a37bf57", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T08:29:51.920157Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:50504 - \"DELETE /api/v1/novels/8aa3d3b6-a99f-470b-b567-224395d741d6 HTTP/1.1\" 200", "request_id": "0db9d934", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T08:29:51.948789Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:50504 - \"DELETE /api/v1/novels/e9bb8afd-b07c-41d3-b580-a6b60a6dda6e HTTP/1.1\" 200", "request_id": "fa0aaf9b", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T08:29:51.976375Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:50504 - \"DELETE /api/v1/novels/26b407f5-d89c-4c21-a978-70b599002abf HTTP/1.1\" 200", "request_id": "8d9ab5dc", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T08:29:52.002370Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:50504 - \"DELETE /api/v1/novels/02d96a63-c8e0-44fa-b9d3-afe5ac6b325a HTTP/1.1\" 200", "request_id": "80f470f4", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T08:29:52.036819Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:50504 - \"DELETE /api/v1/novels/daa359e0-ed23-43a7-a6e3-f2f19d33a632 HTTP/1.1\" 200", "request_id": "b6ab72df", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T08:29:52.067912Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:50504 - \"DELETE /api/v1/novels/759e65ab-d985-44c7-a428-bf744e7e8c2b HTTP/1.1\" 200", "request_id": "c21a2c6f", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T08:29:52.097086Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:50504 - \"DELETE /api/v1/novels/f74d5a98-f946-4209-ba57-517c70feb2f3 HTTP/1.1\" 200", "request_id": "e20f7239", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T08:29:52.135120Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:50504 - \"DELETE /api/v1/novels/cf6a239a-cbe2-421f-a899-a5d26a53dfda HTTP/1.1\" 200", "request_id": "0213a85c", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T08:29:52.141116Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:50504 - \"GET /api/v1/auth/me HTTP/1.1\" 200", "request_id": "0af0a567", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T08:29:52.148120Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:50504 - \"GET /api/v1/users/quota HTTP/1.1\" 200", "request_id": "b94715da", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T08:29:57.134792Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:50506 - \"GET /health HTTP/1.1\" 200", "request_id": "108779f7", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T08:29:57.419629Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:50508 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "8f97fba4", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T08:29:57.443634Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:50508 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "134812ff", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T08:29:57.559184Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:50508 - \"POST /api/v1/generation/architecture HTTP/1.1\" 200", "request_id": "e3c7688e", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T08:35:00.702319Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:51230 - \"GET /health HTTP/1.1\" 200", "request_id": "cbfa2060", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T08:35:00.988281Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:51232 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "75d5c0b7", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T08:35:01.027156Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:51232 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "eaca930c", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T08:35:01.131380Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:51232 - \"POST /api/v1/generation/architecture HTTP/1.1\" 200", "request_id": "7cf3e55c", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T09:10:37.198608Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:55182 - \"GET /health HTTP/1.1\" 200", "request_id": "aa0bfba7", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T09:10:37.474141Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:55184 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "5e2969cb", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T09:10:37.513989Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:55184 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "237e1175", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T09:10:37.620096Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:55184 - \"POST /api/v1/generation/architecture HTTP/1.1\" 200", "request_id": "1014e119", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T09:20:07.731209Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:55729 - \"GET /health HTTP/1.1\" 200", "request_id": "e744c9ce", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T09:20:11.541477Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:55731 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "e3923c0d", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T09:20:11.549477Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:55731 - \"GET /api/v1/users/quota HTTP/1.1\" 200", "request_id": "675c6c70", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T09:20:11.572989Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:55731 - \"GET /api/v1/novels/?limit=100 HTTP/1.1\" 200", "request_id": "c16a23f7", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T09:20:11.626725Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:55731 - \"DELETE /api/v1/novels/2f14a027-d37d-4284-aa92-1d149d5dbd1a HTTP/1.1\" 200", "request_id": "e4087966", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T09:20:11.653299Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:55731 - \"DELETE /api/v1/novels/7150fcac-35d0-4aea-bb1f-81da764668c3 HTTP/1.1\" 200", "request_id": "0053057c", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T09:20:11.684318Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:55731 - \"DELETE /api/v1/novels/b2a5665c-43cc-423c-86c9-dd0d6e268c3d HTTP/1.1\" 200", "request_id": "651d2c0e", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T09:20:11.691323Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:55731 - \"GET /api/v1/auth/me HTTP/1.1\" 200", "request_id": "f88c1e66", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T09:20:11.698323Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:55731 - \"GET /api/v1/users/quota HTTP/1.1\" 200", "request_id": "10fab31a", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T09:20:15.899687Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:55734 - \"GET /health HTTP/1.1\" 200", "request_id": "69c3feea", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T09:20:16.184516Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:55736 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "33a15fbd", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T09:20:16.208455Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:55736 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "e7cd4926", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T09:20:16.315120Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:55736 - \"POST /api/v1/generation/architecture HTTP/1.1\" 200", "request_id": "870ae96b", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T12:59:39.417681Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:65457 - \"GET /health HTTP/1.1\" 200", "request_id": "7c854d54", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T12:59:41.495861Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:65460 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "9418af04", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T12:59:41.505859Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:65460 - \"GET /api/v1/users/quota HTTP/1.1\" 200", "request_id": "b9262707", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T12:59:41.537871Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:65460 - \"GET /api/v1/novels/?limit=100 HTTP/1.1\" 200", "request_id": "43d47d30", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T12:59:41.621120Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:65460 - \"DELETE /api/v1/novels/5af7b7a3-2cef-4360-9658-e66f23f84bb7 HTTP/1.1\" 200", "request_id": "aa4e9529", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T12:59:41.629118Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:65460 - \"GET /api/v1/auth/me HTTP/1.1\" 200", "request_id": "e23f67a8", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T12:59:41.636118Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:65460 - \"GET /api/v1/users/quota HTTP/1.1\" 200", "request_id": "7a7a6faa", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T12:59:48.491577Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:65464 - \"GET /health HTTP/1.1\" 200", "request_id": "e32a7fe8", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T12:59:48.774736Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:65466 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "26fc1351", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T12:59:48.800110Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:65466 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "e0db9821", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T12:59:48.924774Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:65466 - \"POST /api/v1/generation/architecture HTTP/1.1\" 200", "request_id": "6bed3483", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T13:44:51.221568Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:55364 - \"GET /health HTTP/1.1\" 200", "request_id": "86613b36", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T13:44:51.509925Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:55366 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "59f02a33", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T13:44:51.550074Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:55366 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "c922c89d", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T13:44:51.654229Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:55366 - \"POST /api/v1/generation/architecture HTTP/1.1\" 200", "request_id": "4a9bdff8", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T17:16:40.236267Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:64408 - \"GET /health HTTP/1.1\" 200", "request_id": "6531afaa", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T17:16:40.540424Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:64411 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "1ca15327", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T17:16:40.597005Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:64411 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "b10550f0", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T17:16:40.779039Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:64411 - \"POST /api/v1/generation/architecture HTTP/1.1\" 200", "request_id": "43145823", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T17:27:14.202004Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:64990 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "eb728c83", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T17:27:14.254521Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:64990 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "e6d4fff7", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T17:27:14.445403Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:64990 - \"POST /api/v1/generation/architecture HTTP/1.1\" 200", "request_id": "befbf37b", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T18:03:26.869675Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63331 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "a9dae3dd", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T18:03:26.937715Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63331 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "5dd42f5e", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T18:03:27.126142Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63331 - \"POST /api/v1/generation/architecture HTTP/1.1\" 200", "request_id": "3cc29971", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T18:12:29.672899Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63900 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "86c8c534", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T18:12:29.710397Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63900 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "2d9e7d43", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T18:12:29.816843Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:63900 - \"POST /api/v1/generation/architecture HTTP/1.1\" 200", "request_id": "aeef5c08", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T18:26:54.746289Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:64650 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "44bfba31", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T18:26:54.785386Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:64650 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "69005331", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T18:26:54.908621Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:64650 - \"POST /api/v1/generation/architecture HTTP/1.1\" 200", "request_id": "962e9963", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T18:35:01.745945Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:65028 - \"GET /docs HTTP/1.1\" 200", "request_id": "7ae7f809", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T18:35:01.780460Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:65029 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "807bf70a", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T18:35:01.816977Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:65029 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "992b7e9a", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T18:35:01.845544Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:65029 - \"POST /api/v1/generation/architecture HTTP/1.1\" 200", "request_id": "c1068d6d", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T18:41:53.022222Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:65367 - \"GET /docs HTTP/1.1\" 200", "request_id": "d30bb813", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T18:41:53.056222Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:65368 - \"POST /api/v1/auth/test/login HTTP/1.1\" 200", "request_id": "34fd3e72", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T18:41:53.091839Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:65368 - \"POST /api/v1/novels/ HTTP/1.1\" 200", "request_id": "f4320d52", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
{"timestamp": "2025-06-20T18:41:53.117772Z", "level": "INFO", "name": "uvicorn.access", "message": "127.0.0.1:65368 - \"POST /api/v1/generation/architecture HTTP/1.1\" 200", "request_id": "ca8eb921", "user_id": "anonymous", "operation": "unknown", "module": "httptools_impl", "function": "send", "line": 496, "service": "AI Novel Generator", "version": "1.0.0", "environment": "production"}
