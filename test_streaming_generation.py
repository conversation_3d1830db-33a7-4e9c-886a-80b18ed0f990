#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试流式生成和详细状态显示功能
"""
import asyncio
import websockets
import json
import requests
from datetime import datetime
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='[%(asctime)s] %(levelname)s: %(message)s', 
                   datefmt='%H:%M:%S')
logger = logging.getLogger(__name__)

# 配置
API_BASE_URL = "http://localhost:8000/api/v1"
WS_BASE_URL = "ws://localhost:8000"

class StreamingGenerationTester:
    def __init__(self):
        self.access_token = None
        self.user_id = None
        
    def login(self):
        """登录获取token"""
        try:
            response = requests.post(f"{API_BASE_URL}/auth/wechat/login", json={
                "code": "test_code_streaming_test",
                "nickname": "流式测试用户",
                "avatar_url": "https://example.com/avatar.jpg"
            })
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    self.access_token = data["data"]["access_token"]
                    self.user_id = data["data"]["user"]["id"]
                    logger.info(f"✅ 登录成功，用户ID: {self.user_id}")
                    return True
                else:
                    logger.error(f"❌ 登录失败: {data}")
                    return False
            else:
                logger.error(f"❌ 登录请求失败: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 登录异常: {e}")
            return False
    
    def create_novel(self):
        """创建测试小说"""
        try:
            headers = {"Authorization": f"Bearer {self.access_token}"}
            novel_data = {
                "title": "流式生成测试小说",
                "description": "这是一个测试流式生成功能的科幻小说，讲述未来世界中AI与人类共存的故事",
                "genre": "science_fiction",
                "target_length": 50000,
                "style_settings": {
                    "style": "科幻现实主义",
                    "pace": "medium",
                    "focus": "character_development"
                }
            }
            
            response = requests.post(f"{API_BASE_URL}/novels/", 
                                   json=novel_data, headers=headers)
            
            if response.status_code == 201:
                data = response.json()
                if data.get("success"):
                    novel_id = data["data"]["id"]
                    logger.info(f"✅ 小说创建成功，ID: {novel_id}")
                    return novel_id
                else:
                    logger.error(f"❌ 小说创建失败: {data}")
                    return None
            else:
                logger.error(f"❌ 小说创建请求失败: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"❌ 小说创建异常: {e}")
            return None
    
    def start_architecture_generation(self, novel_id):
        """启动架构生成任务"""
        try:
            headers = {"Authorization": f"Bearer {self.access_token}"}
            generation_data = {
                "novel_id": novel_id,
                "theme": "未来世界中AI与人类的关系探索",
                "genre": "science_fiction",
                "target_length": 50000,
                "style_preferences": {
                    "style": "科幻现实主义",
                    "pace": "medium",
                    "focus": "character_development"
                }
            }
            
            response = requests.post(f"{API_BASE_URL}/generation/architecture", 
                                   json=generation_data, headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    task_id = data["data"]["task_id"]
                    logger.info(f"✅ 架构生成任务启动成功，任务ID: {task_id}")
                    return task_id
                else:
                    logger.error(f"❌ 架构生成任务启动失败: {data}")
                    return None
            else:
                logger.error(f"❌ 架构生成请求失败: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"❌ 架构生成任务启动异常: {e}")
            return None
    
    async def monitor_task_progress(self, task_id, timeout=600):
        """通过WebSocket监控任务进度"""
        uri = f"{WS_BASE_URL}/ws/{task_id}"
        
        try:
            async with websockets.connect(uri) as websocket:
                logger.info(f"🔗 WebSocket连接成功，监控任务: {task_id}")
                
                # 发送心跳包
                ping_message = {
                    "type": "ping",
                    "timestamp": datetime.utcnow().isoformat() + "Z"
                }
                await websocket.send(json.dumps(ping_message))
                
                start_time = asyncio.get_event_loop().time()
                
                while True:
                    try:
                        # 设置接收超时
                        message = await asyncio.wait_for(
                            websocket.recv(), timeout=30.0
                        )
                        
                        data = json.loads(message)
                        await self.handle_websocket_message(data)
                        
                        # 检查任务是否完成
                        if data.get("status") in ["SUCCESS", "FAILED"]:
                            logger.info(f"🏁 任务完成，状态: {data.get('status')}")
                            return data.get("status") == "SUCCESS"
                        
                        # 检查总超时
                        if asyncio.get_event_loop().time() - start_time > timeout:
                            logger.warning(f"⏰ 任务监控超时 ({timeout}秒)")
                            return False
                            
                    except asyncio.TimeoutError:
                        logger.warning("⏰ WebSocket接收超时，发送心跳包")
                        await websocket.send(json.dumps(ping_message))
                        
        except Exception as e:
            logger.error(f"❌ WebSocket连接异常: {e}")
            return False
    
    async def handle_websocket_message(self, data):
        """处理WebSocket消息"""
        message_type = data.get("type")
        
        if message_type == "task_update":
            status = data.get("status")
            progress = data.get("progress", 0)
            message = data.get("message", "")
            current_stage = data.get("current_stage", "")
            detailed_progress = data.get("detailed_progress", {})
            
            # 构造详细的进度信息
            stage_info = ""
            if current_stage:
                stage_info = f" [{current_stage}]"
            
            detail_info = ""
            if detailed_progress:
                step = detailed_progress.get("step", "")
                step_name = detailed_progress.get("step_name", "")
                generating = detailed_progress.get("generating", False)
                
                if step and step_name:
                    detail_info = f" - {step}: {step_name}"
                    if generating:
                        detail_info += " (实时生成中)"
            
            logger.info(f"📈 进度更新: {progress}%{stage_info}{detail_info} - {message}")
            
            # 如果有数据，显示部分内容
            if data.get("data") and status == "SUCCESS":
                result_data = data["data"]
                word_count = result_data.get("word_count", 0)
                logger.info(f"✨ 生成完成! 总字数: {word_count}")
                
        elif message_type == "pong":
            logger.info("💓 心跳响应收到")
            
        elif message_type == "task_cancel_received":
            logger.info("🚫 任务取消确认收到")
    
    async def run_test(self):
        """运行完整测试"""
        logger.info("🚀 开始流式生成测试")
        
        # 1. 登录
        if not self.login():
            logger.error("💥 测试失败 - 登录失败")
            return False
        
        # 2. 创建小说
        novel_id = self.create_novel()
        if not novel_id:
            logger.error("💥 测试失败 - 小说创建失败")
            return False
        
        # 3. 启动架构生成
        task_id = self.start_architecture_generation(novel_id)
        if not task_id:
            logger.error("💥 测试失败 - 架构生成启动失败")
            return False
        
        # 4. 监控任务进度
        logger.info("📡 开始监控任务进度...")
        success = await self.monitor_task_progress(task_id, timeout=600)
        
        if success:
            logger.info("🎉 测试成功完成！")
            return True
        else:
            logger.error("💥 测试失败 - 任务执行失败或超时")
            return False

async def main():
    """主函数"""
    tester = StreamingGenerationTester()
    success = await tester.run_test()
    
    if success:
        print("\n✅ 流式生成测试通过！")
    else:
        print("\n❌ 流式生成测试失败！")
    
    return success

if __name__ == "__main__":
    asyncio.run(main()) 