#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试任务执行的调试脚本
"""
import asyncio
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

async def test_task_execution():
    """测试任务执行流程"""
    print("=== 任务执行调试测试 ===")
    
    # 1. 测试环境变量
    print("\n1. 检查关键环境变量:")
    env_vars = [
        'LLM_PROVIDER', 'LLM_API_KEY', 'LLM_BASE_URL', 'LLM_MODEL',
        'DATABASE_URL', 'REDIS_URL'
    ]
    
    for var in env_vars:
        value = os.environ.get(var)
        if var == 'LLM_API_KEY' and value:
            print(f"  {var}: {'*' * 10} (已设置)")
        else:
            print(f"  {var}: {value}")
    
    # 2. 测试LLM服务初始化
    print("\n2. 测试LLM服务初始化:")
    try:
        from app.services.llm_service import initialize_default_llm_service
        llm_initialized = await initialize_default_llm_service()
        print(f"  LLM服务初始化: {'成功' if llm_initialized else '失败'}")
        
        if llm_initialized:
            print("  测试LLM调用:")
            from app.services.llm_service import llm_service
            test_result = await llm_service.invoke_with_retry("测试提示词：请回复'测试成功'")
            print(f"  LLM测试响应: {test_result[:50]}...")
    except Exception as e:
        print(f"  LLM服务测试失败: {e}")
    
    # 3. 测试数据库连接
    print("\n3. 测试数据库连接:")
    try:
        from app.core.database import AsyncSessionLocal
        async with AsyncSessionLocal() as session:
            result = await session.execute("SELECT 1 as test")
            print(f"  数据库连接: 成功")
    except Exception as e:
        print(f"  数据库连接失败: {e}")
    
    # 4. 测试Redis连接
    print("\n4. 测试Redis连接:")
    try:
        import redis
        r = redis.Redis.from_url(os.environ.get('REDIS_URL', 'redis://localhost:6379/0'))
        r.ping()
        print(f"  Redis连接: 成功")
    except Exception as e:
        print(f"  Redis连接失败: {e}")
    
    # 5. 测试Celery任务
    print("\n5. 测试Celery任务:")
    try:
        from app.services.generation_tasks import generate_novel_architecture
        print(f"  任务函数导入: 成功")
        print(f"  任务名称: {generate_novel_architecture.name}")
    except Exception as e:
        print(f"  Celery任务测试失败: {e}")
    
    print("\n=== 调试测试完成 ===")

if __name__ == "__main__":
    # 设置基本环境变量
    os.environ.setdefault('LLM_PROVIDER', 'openai')
    os.environ.setdefault('LLM_API_KEY', 'your-api-key-here')
    os.environ.setdefault('LLM_BASE_URL', 'https://api.openai.com/v1')
    os.environ.setdefault('LLM_MODEL', 'gpt-3.5-turbo')
    os.environ.setdefault('DATABASE_URL', 'postgresql+asyncpg://novel_user:novel_password@localhost:5432/ai_novel_generator')
    os.environ.setdefault('REDIS_URL', 'redis://localhost:6379/0')
    
    asyncio.run(test_task_execution()) 