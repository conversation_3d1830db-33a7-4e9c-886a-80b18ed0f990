#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版WebSocket测试脚本 - 专门测试流式内容
"""

import asyncio
import json
import time
from datetime import datetime
import aiohttp
import websockets


class SimpleWebSocketTester:
    """简化版WebSocket测试器"""
    
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.ws_url = "ws://localhost:8000"
        self.api_prefix = "/api/v1"
        self.session = None
        self.access_token = None
        self.novel_id = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=300))
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    def _log(self, message: str):
        """日志输出"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {message}")
    
    async def _make_request(self, method: str, endpoint: str, data=None):
        """发送HTTP请求"""
        url = f"{self.base_url}{self.api_prefix}{endpoint}"
        headers = {"Content-Type": "application/json"}
        if self.access_token:
            headers["Authorization"] = f"Bearer {self.access_token}"
        
        self._log(f"{method} {url}")
        
        async with self.session.request(method, url, headers=headers, json=data) as response:
            response_data = await response.json()
            
            if response.status >= 400:
                self._log(f"请求失败 [{response.status}]: {response_data}")
                raise Exception(f"API请求失败: {response_data}")
            
            return response_data
    
    async def login(self):
        """用户登录"""
        self._log("开始用户登录...")
        
        login_data = {
            "code": "test_code_123456",
            "nickname": "AI小说测试用户",
            "avatar_url": "https://example.com/avatar.jpg"
        }
        
        response = await self._make_request("POST", "/auth/test/login", login_data)
        
        if "access_token" in response:
            self.access_token = response["access_token"]
            user_info = response["user"]
            self._log(f"登录成功! 用户ID: {user_info['id']}")
            return True
        else:
            self._log(f"登录失败: {response}")
            return False
    
    async def create_novel(self):
        """创建小说"""
        self._log("创建小说项目...")
        
        novel_data = {
            "title": "AI奇幻冒险小说",
            "description": "一个现代程序员意外穿越到魔法世界，必须用编程思维拯救这个被古老诅咒笼罩的王国",
            "genre": "fantasy",
            "target_length": 25000,
            "style_settings": {
                "style": "奇幻冒险",
                "pace": "medium",
                "focus": "plot_driven"
            }
        }
        
        response = await self._make_request("POST", "/novels/", novel_data)
        
        if "id" in response:
            self.novel_id = response["id"]
            self._log(f"小说创建成功! 小说ID: {self.novel_id}")
            return True
        else:
            self._log(f"小说创建失败: {response}")
            return False
    
    async def websocket_listener(self, task_id: str):
        """WebSocket监听器"""
        ws_url = f"{self.ws_url}/ws/{task_id}"
        self._log(f"连接WebSocket: {ws_url}")
        
        try:
            async with websockets.connect(ws_url) as websocket:
                self._log("WebSocket连接成功")
                
                async for message in websocket:
                    try:
                        data = json.loads(message)
                        message_type = data.get("type", "unknown")
                        
                        self._log(f"收到消息: {message_type}")
                        
                        if message_type == "task_update":
                            status = data.get("status", "UNKNOWN")
                            progress = data.get("progress", 0)
                            msg = data.get("message", "")
                            self._log(f"任务更新: {status} - {progress}% - {msg}")
                            
                            if status in ["SUCCESS", "FAILED", "CANCELLED"]:
                                self._log(f"任务完成: {status}")
                                break
                                
                        elif message_type == "stage_start":
                            stage = data.get("stage", "unknown")
                            self._log(f"开始阶段: {stage}")
                            
                        elif message_type == "content_chunk":
                            stage = data.get("stage", "unknown")
                            chunk = data.get("chunk", "")
                            chunk_index = data.get("chunk_index", 0)
                            
                            # 显示流式内容片段
                            display_chunk = chunk[:100] + "..." if len(chunk) > 100 else chunk
                            self._log(f"[{stage}] 片段{chunk_index}: {display_chunk}")
                            
                        elif message_type == "content_complete":
                            stage = data.get("stage", "unknown")
                            final_content = data.get("final_content", "")
                            self._log(f"阶段完成: {stage} (生成{len(final_content)}字符)")
                            
                        elif message_type == "generation_complete":
                            summary = data.get("summary", "生成完成")
                            self._log(f"生成完成: {summary}")
                            break
                            
                        else:
                            self._log(f"其他消息: {message_type} - {data}")
                            
                    except json.JSONDecodeError:
                        self._log(f"无法解析消息: {message}")
                        
        except Exception as e:
            self._log(f"WebSocket连接异常: {e}")
    
    async def generate_architecture(self):
        """生成小说架构"""
        self._log("生成小说架构...")
        
        arch_data = {
            "novel_id": self.novel_id,
            "theme": "一个现代程序员意外穿越到魔法世界，必须用编程思维拯救这个被古老诅咒笼罩的王国",
            "genre": "fantasy",
            "target_length": 25000,
            "style_preferences": {
                "style": "奇幻冒险",
                "pace": "medium",
                "focus": "character_development"
            }
        }
        
        response = await self._make_request("POST", "/generation/architecture", arch_data)
        
        if "task_id" in response:
            task_id = response["task_id"]
            self._log(f"架构生成任务启动! 任务ID: {task_id}")
            
            # 启动WebSocket监听
            await self.websocket_listener(task_id)
            return True
        else:
            self._log(f"架构生成失败: {response}")
            return False
    
    async def run_test(self):
        """运行测试"""
        self._log("开始WebSocket流式内容测试")
        self._log("=" * 50)
        
        try:
            # 1. 登录
            if not await self.login():
                return False
            
            # 2. 创建小说
            if not await self.create_novel():
                return False
            
            # 3. 生成架构（包含WebSocket监听）
            if not await self.generate_architecture():
                return False
            
            self._log("=" * 50)
            self._log("测试完成!")
            return True
            
        except Exception as e:
            self._log(f"测试异常: {e}")
            return False


async def main():
    """主函数"""
    print("WebSocket流式内容测试")
    print("=" * 50)
    
    async with SimpleWebSocketTester() as tester:
        success = await tester.run_test()
        
        if success:
            print("测试成功!")
            return 0
        else:
            print("测试失败!")
            return 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        exit(exit_code)
    except KeyboardInterrupt:
        print("\n用户中断测试")
        exit(0)
