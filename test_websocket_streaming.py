#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
WebSocket流式生成测试脚本
"""
import asyncio
import websockets
import json
import requests
from datetime import datetime
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='[%(asctime)s] %(levelname)s: %(message)s', 
                   datefmt='%H:%M:%S')
logger = logging.getLogger(__name__)

# 配置
API_BASE_URL = "http://localhost:8000/api/v1"
WS_BASE_URL = "ws://localhost:8000"

class WebSocketStreamingTester:
    def __init__(self):
        self.access_token = None
        self.user_id = None
        self.current_task_id = None
        
    def login(self):
        """登录获取token"""
        try:
            response = requests.post(f"{API_BASE_URL}/auth/test/login", json={
                "code": "test_code_websocket_streaming",
                "nickname": "WebSocket流式测试用户",
                "avatar_url": "https://example.com/avatar.jpg"
            })
            
            if response.status_code == 200:
                data = response.json()
                self.access_token = data["access_token"]
                self.user_id = data["user"]["id"]
                logger.info(f"✅ 登录成功! 用户ID: {self.user_id}")
                logger.info(f"📊 配额状态: {data['user']['quota_used']}/{data['user']['quota_limit']}")
                return True
            else:
                logger.error(f"❌ 登录失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 登录异常: {e}")
            return False
    
    def create_novel(self):
        """创建测试小说"""
        try:
            response = requests.post(f"{API_BASE_URL}/novels/", 
                headers={"Authorization": f"Bearer {self.access_token}"},
                json={
                    "title": "WebSocket流式测试小说",
                    "description": "测试WebSocket实时更新和流式生成功能",
                    "genre": "science_fiction",
                    "target_length": 8000,
                    "style_settings": {
                        "style": "实时科幻",
                        "pace": "fast",
                        "focus": "streaming_experience"
                    }
                }
            )
            
            if response.status_code == 200:
                novel_data = response.json()
                novel_id = novel_data["id"]
                logger.info(f"✅ 小说创建成功! ID: {novel_id}")
                logger.info(f"📖 标题: {novel_data['title']}")
                return novel_id
            else:
                logger.error(f"❌ 小说创建失败: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"❌ 小说创建异常: {e}")
            return None
    
    def start_architecture_generation(self, novel_id):
        """启动架构生成任务"""
        try:
            response = requests.post(f"{API_BASE_URL}/generation/architecture",
                headers={"Authorization": f"Bearer {self.access_token}"},
                json={
                    "novel_id": novel_id,
                    "theme": "2099年，AI与人类协作探索宇宙的冒险故事",
                    "genre": "science_fiction",
                    "target_length": 8000,
                    "style_preferences": {
                        "style": "实时科幻",
                        "pace": "fast",
                        "focus": "streaming_development"
                    }
                }
            )
            
            if response.status_code == 200:
                task_data = response.json()
                task_id = task_data["task_id"]
                self.current_task_id = task_id
                logger.info(f"✅ 架构生成任务启动! 任务ID: {task_id}")
                return task_id
            else:
                logger.error(f"❌ 任务启动失败: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"❌ 任务启动异常: {e}")
            return None
    
    async def connect_websocket_and_monitor(self, task_id):
        """连接WebSocket并监控任务进度"""
        ws_url = f"{WS_BASE_URL}/ws/{task_id}"
        logger.info(f"🔌 连接WebSocket: {ws_url}")
        
        try:
            async with websockets.connect(ws_url) as websocket:
                logger.info("✅ WebSocket连接成功!")
                
                # 发送心跳包
                await websocket.send(json.dumps({
                    "type": "ping",
                    "timestamp": datetime.now().isoformat()
                }))
                logger.info("💓 心跳包已发送")
                
                # 监听消息
                message_count = 0
                start_time = datetime.now()
                
                async for message in websocket:
                    try:
                        data = json.loads(message)
                        message_count += 1
                        current_time = datetime.now()
                        elapsed = (current_time - start_time).total_seconds()
                        
                        logger.info(f"📨 [{message_count:02d}] [{elapsed:6.1f}s] 收到消息: {data.get('type', 'unknown')}")
                        
                        if data.get("type") == "pong":
                            logger.info("💓 心跳响应收到")
                            
                        elif data.get("type") == "task_update":
                            self.handle_task_update(data, elapsed)
                            
                            # 任务完成或失败时断开连接
                            if data.get("status") in ["SUCCESS", "FAILED", "CANCELLED"]:
                                logger.info(f"🏁 任务结束，状态: {data.get('status')}")
                                break
                                
                        elif data.get("type") == "task_cancel_received":
                            logger.info("🛑 任务取消确认收到")
                            
                        else:
                            logger.info(f"❓ 未知消息类型: {data}")
                            
                    except json.JSONDecodeError as e:
                        logger.error(f"❌ JSON解析失败: {e}")
                        logger.error(f"原始消息: {message}")
                        
                    except Exception as e:
                        logger.error(f"❌ 消息处理异常: {e}")
                
                logger.info(f"📊 WebSocket会话总结:")
                logger.info(f"   - 总消息数: {message_count}")
                logger.info(f"   - 总耗时: {elapsed:.1f}秒")
                logger.info(f"   - 平均频率: {message_count/elapsed:.2f} 消息/秒")
                
        except websockets.exceptions.ConnectionClosed as e:
            logger.warning(f"🔌 WebSocket连接关闭: {e}")
        except Exception as e:
            logger.error(f"❌ WebSocket连接异常: {e}")
    
    def handle_task_update(self, data, elapsed_time):
        """处理任务更新消息"""
        task_id = data.get("task_id", "unknown")
        status = data.get("status", "unknown")
        progress = data.get("progress", 0)
        message = data.get("message", "")
        current_stage = data.get("current_stage")
        detailed_progress = data.get("detailed_progress")
        
        # 基本信息
        logger.info(f"📈 任务更新: {status} ({progress}%)")
        if message:
            logger.info(f"💬 消息: {message}")
        
        # 详细状态信息 
        if current_stage:
            logger.info(f"🎯 当前阶段: {current_stage}")
            
        if detailed_progress:
            logger.info(f"📋 详细进度: {detailed_progress}")
        
        # 计算速度估算
        if progress > 0 and elapsed_time > 0:
            estimated_total = elapsed_time * 100 / progress
            remaining = estimated_total - elapsed_time
            logger.info(f"⏱️  预估剩余时间: {remaining:.1f}秒")
        
        # 额外数据
        task_data = data.get("data", {})
        if task_data:
            logger.info(f"📦 任务数据: {task_data}")
    
    def get_final_result(self, task_id):
        """获取最终结果"""
        try:
            response = requests.get(f"{API_BASE_URL}/tasks/{task_id}",
                headers={"Authorization": f"Bearer {self.access_token}"})
                
            if response.status_code == 200:
                task_data = response.json()
                logger.info(f"📋 最终任务状态:")
                logger.info(f"   - 状态: {task_data.get('status')}")
                logger.info(f"   - 进度: {task_data.get('progress')}%")
                logger.info(f"   - 当前阶段: {task_data.get('current_stage', '未设置')}")
                
                if task_data.get('detailed_progress'):
                    logger.info(f"   - 详细进度: {task_data.get('detailed_progress')}")
                    
                if task_data.get('error_message'):
                    logger.error(f"   - 错误信息: {task_data.get('error_message')}")
                    
                return task_data
            else:
                logger.error(f"❌ 获取任务状态失败: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"❌ 获取任务状态异常: {e}")
            return None

async def main():
    """主函数"""
    logger.info("🚀 开始WebSocket流式生成测试")
    logger.info("=" * 60)
    
    tester = WebSocketStreamingTester()
    
    # 1. 登录
    logger.info("🔐 步骤1: 用户登录...")
    if not tester.login():
        logger.error("❌ 登录失败，测试终止")
        return
    
    # 2. 创建小说
    logger.info("📚 步骤2: 创建测试小说...")
    novel_id = tester.create_novel()
    if not novel_id:
        logger.error("❌ 小说创建失败，测试终止")
        return
    
    # 3. 启动架构生成
    logger.info("🏗️ 步骤3: 启动架构生成任务...")
    task_id = tester.start_architecture_generation(novel_id)
    if not task_id:
        logger.error("❌ 任务启动失败，测试终止")
        return
    
    # 4. WebSocket监控
    logger.info("📡 步骤4: 开始WebSocket实时监控...")
    await tester.connect_websocket_and_monitor(task_id)
    
    # 5. 获取最终结果
    logger.info("📊 步骤5: 获取最终结果...")
    final_result = tester.get_final_result(task_id)
    
    logger.info("=" * 60)
    logger.info("🎉 WebSocket流式生成测试完成!")
    
    if final_result and final_result.get('status') == 'SUCCESS':
        logger.info("✅ 测试成功: 流式生成和详细状态显示正常工作")
    else:
        logger.warning("⚠️  测试警告: 可能存在问题需要进一步检查")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("🛑 用户中断测试")
    except Exception as e:
        logger.error(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc() 