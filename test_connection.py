#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的API连接测试脚本
"""
import requests
import json
import sys
import time


def test_api_connection():
    """测试API连接"""
    print("🔍 测试API连接...")
    
    # 测试健康检查端点
    try:
        print("📡 测试健康检查端点...")
        response = requests.get("http://localhost:8000/health", timeout=10)
        print(f"✅ 健康检查成功: {response.status_code}")
        print(f"📄 响应内容: {response.text}")
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ 连接被拒绝 - API服务可能未启动")
        return False
        
    except requests.exceptions.Timeout:
        print("❌ 连接超时 - API服务响应缓慢")
        return False
        
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False


def test_root_endpoint():
    """测试根端点"""
    print("\n🔍 测试根端点...")
    
    try:
        response = requests.get("http://localhost:8000/", timeout=10)
        print(f"✅ 根端点成功: {response.status_code}")
        print(f"📄 响应内容: {response.text}")
        return True
        
    except Exception as e:
        print(f"❌ 根端点失败: {e}")
        return False


def test_docs_endpoint():
    """测试文档端点"""
    print("\n🔍 测试API文档端点...")
    
    try:
        response = requests.get("http://localhost:8000/docs", timeout=10)
        print(f"✅ 文档端点成功: {response.status_code}")
        return True
        
    except Exception as e:
        print(f"❌ 文档端点失败: {e}")
        return False


def test_test_login():
    """测试登录端点"""
    print("\n🔍 测试登录端点...")
    
    try:
        login_data = {
            "code": "test_code_123",
            "nickname": "测试用户",
            "avatar_url": "https://example.com/avatar.jpg"
        }
        
        response = requests.post(
            "http://localhost:8000/api/v1/auth/test/login",
            json=login_data,
            timeout=10
        )
        
        print(f"✅ 登录端点成功: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"📄 登录响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"❌ 登录失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 登录端点失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 API连接测试工具")
    print("=" * 40)
    
    # 等待一下，确保服务完全启动
    print("⏳ 等待服务启动...")
    time.sleep(3)
    
    tests = [
        ("健康检查", test_api_connection),
        ("根端点", test_root_endpoint),
        ("文档端点", test_docs_endpoint),
        ("登录端点", test_test_login),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        if test_func():
            passed += 1
        time.sleep(1)  # 间隔1秒
    
    print(f"\n{'='*40}")
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！API服务正常运行")
        return 0
    else:
        print("❌ 部分测试失败，请检查API服务状态")
        return 1


if __name__ == "__main__":
    sys.exit(main())
