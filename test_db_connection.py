#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试数据库连接
"""
import asyncio
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

async def test_database_connections():
    """测试各种数据库连接配置"""
    print("=== 数据库连接测试 ===")
    
    # 测试配置列表
    test_configs = [
        ("项目默认配置", "postgresql+asyncpg://novel_user:novel_password@localhost:5432/ai_novel_generator"),
        ("Worker脚本旧配置", "postgresql+asyncpg://user:password@localhost/novel_db"),
        ("Worker脚本新配置", "postgresql+asyncpg://novel_user:novel_password@localhost:5432/ai_novel_generator"),
    ]
    
    for config_name, database_url in test_configs:
        print(f"\n测试 {config_name}:")
        print(f"  数据库URL: {database_url}")
        
        try:
            # 设置环境变量
            os.environ['DATABASE_URL'] = database_url
            
            # 测试数据库连接
            from app.core.database import AsyncSessionLocal
            from sqlalchemy import text
            
            async with AsyncSessionLocal() as session:
                result = await session.execute(text("SELECT 1 as test"))
                row = result.fetchone()
                print(f"  连接测试: ✅ 成功 (返回: {row[0]})")
                
        except Exception as e:
            print(f"  连接测试: ❌ 失败")
            error_msg = str(e)
            if "password authentication failed" in error_msg:
                print(f"    错误: 用户名或密码认证失败")
            elif "database" in error_msg and "does not exist" in error_msg:
                print(f"    错误: 数据库不存在")
            elif "connection refused" in error_msg:
                print(f"    错误: 连接被拒绝（数据库服务未运行）")
            else:
                print(f"    错误: {error_msg}")

async def test_current_environment():
    """测试当前环境的数据库配置"""
    print("\n=== 当前环境配置测试 ===")
    
    # 从配置文件读取
    try:
        from app.core.config import settings
        print(f"配置文件中的数据库URL: {settings.DATABASE_URL}")
        
        # 测试连接
        from app.core.database import AsyncSessionLocal
        from sqlalchemy import text
        
        async with AsyncSessionLocal() as session:
            result = await session.execute(text("SELECT version()"))
            version = result.fetchone()
            print(f"数据库版本: {version[0]}")
            print("当前环境数据库连接: ✅ 成功")
            
    except Exception as e:
        print(f"当前环境数据库连接: ❌ 失败")
        print(f"错误: {e}")

if __name__ == "__main__":
    asyncio.run(test_database_connections())
    asyncio.run(test_current_environment()) 