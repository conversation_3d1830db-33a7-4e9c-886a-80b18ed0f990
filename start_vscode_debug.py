#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI小说生成器 - VSCode调试专用启动脚本
专门为VSCode调试环境优化
"""
import os
import sys
import subprocess
import threading
import time
import signal


class VSCodeDebugManager:
    """VSCode调试管理器 - 专门为VSCode调试环境优化"""

    def __init__(self):
        self.api_process = None
        self.worker_process = None
        self.running = True

    def log(self, service, message):
        """统一日志输出"""
        timestamp = time.strftime('%H:%M:%S')
        prefix = f"[{service.upper()}]"
        print(f"{timestamp} {prefix} {message}")

    def check_environment(self):
        """检查运行环境"""
        print("🔍 VSCode调试环境检查...")

        # 检查是否在项目根目录
        if not os.path.exists("app"):
            print("❌ 错误：请在项目根目录运行此脚本")
            return False

        # 检查app/main.py是否存在
        if not os.path.exists("app/main.py"):
            print("❌ 错误：app/main.py 不存在")
            return False

        # 检查app/core/celery_app.py是否存在
        if not os.path.exists("app/core/celery_app.py"):
            print("❌ 错误：app/core/celery_app.py 不存在")
            return False

        print(f"✅ Python版本: {sys.version}")
        print(f"✅ 工作目录: {os.getcwd()}")
        print(f"✅ Python解释器: {sys.executable}")
        
        # 检查调试环境
        if 'debugpy' in sys.modules:
            print("✅ VSCode调试环境已激活")
        else:
            print("⚠️ 未检测到VSCode调试环境")

        return True

    def get_python_executable(self):
        """获取正确的Python解释器路径 - VSCode调试优化版"""
        # 在VSCode调试环境中，直接使用当前解释器
        current_python = os.path.abspath(sys.executable)
        
        # 检查是否在虚拟环境中
        if 'novel_ai_venv' in current_python:
            self.log('system', f"✓ VSCode调试使用虚拟环境: {current_python}")
            return current_python
        
        # 如果不在虚拟环境中，尝试找到虚拟环境
        current_dir = os.getcwd()
        venv_names = ['novel_ai_venv', 'novel_ai_env', 'venv', 'env']
        
        for venv_name in venv_names:
            local_venv = os.path.join(current_dir, venv_name)
            if os.path.exists(local_venv):
                if os.name == 'nt':  # Windows
                    local_python = os.path.join(local_venv, 'Scripts', 'python.exe')
                else:
                    local_python = os.path.join(local_venv, 'bin', 'python')

                if os.path.exists(local_python):
                    local_python = os.path.abspath(local_python)
                    self.log('system', f"✓ 找到虚拟环境Python: {local_python}")
                    return local_python

        self.log('system', f"⚠️ 使用当前Python解释器: {current_python}")
        return current_python

    def start_api_server(self):
        """启动API服务器 - VSCode调试优化版"""
        self.log('api', "正在启动API服务器...")

        # 设置环境变量
        env = os.environ.copy()
        env['PYTHONPATH'] = os.getcwd()
        env['PYTHONIOENCODING'] = 'utf-8'
        env['PYTHONUNBUFFERED'] = '1'  # 确保输出不被缓冲

        # 获取正确的Python解释器
        python_exe = self.get_python_executable()

        # 构建启动命令
        cmd = [
            python_exe, "-m", "uvicorn",
            "app.main:app",
            "--host=0.0.0.0",
            "--port=8000",
            "--reload",
            "--log-level=info"
        ]

        self.log('api', f"执行命令: {' '.join(cmd)}")

        try:
            # 启动API服务
            self.api_process = subprocess.Popen(
                cmd,
                cwd=os.getcwd(),
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=0,  # 无缓冲
                encoding='utf-8',
                errors='replace',
                env=env
            )
            self.log('api', f"API服务已启动，PID: {self.api_process.pid}")
            self.log('api', "服务地址: http://localhost:8000")
            self.log('api', "API文档: http://localhost:8000/docs")

            # 启动日志输出线程
            threading.Thread(
                target=self.log_output_thread,
                args=(self.api_process, 'api'),
                daemon=True
            ).start()

            return True

        except Exception as e:
            self.log('api', f"启动失败: {e}")
            return False

    def start_celery_worker(self):
        """启动Celery Worker - VSCode调试优化版"""
        self.log('worker', "正在启动Celery Worker...")

        # 设置环境变量
        env = os.environ.copy()
        env['PYTHONPATH'] = os.getcwd()
        env['PYTHONIOENCODING'] = 'utf-8'
        env['PYTHONUNBUFFERED'] = '1'  # 确保输出不被缓冲

        # 获取正确的Python解释器
        python_exe = self.get_python_executable()

        # 构建启动命令
        cmd = [
            python_exe, "-m", "celery",
            "-A", "app.core.celery_app:celery_app",
            "worker",
            "--loglevel=info",
            "--pool=solo"
        ]

        self.log('worker', f"执行命令: {' '.join(cmd)}")

        try:
            # 启动Celery Worker
            self.worker_process = subprocess.Popen(
                cmd,
                cwd=os.getcwd(),
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=0,  # 无缓冲
                encoding='utf-8',
                errors='replace',
                env=env
            )
            self.log('worker', f"Celery Worker已启动，PID: {self.worker_process.pid}")

            # 启动日志输出线程
            threading.Thread(
                target=self.log_output_thread,
                args=(self.worker_process, 'worker'),
                daemon=True
            ).start()

            return True

        except Exception as e:
            self.log('worker', f"启动失败: {e}")
            return False

    def log_output_thread(self, process, service):
        """日志输出线程 - VSCode调试优化版"""
        try:
            for line in iter(process.stdout.readline, ''):
                if line.strip() and self.running:
                    try:
                        clean_line = line.strip()
                        if clean_line:
                            self.log(service, clean_line)
                            # 强制刷新输出，确保在VSCode中可见
                            sys.stdout.flush()
                    except UnicodeError:
                        safe_line = line.strip().encode('utf-8', errors='ignore').decode('utf-8')
                        if safe_line:
                            self.log(service, safe_line)
                            sys.stdout.flush()
        except Exception as e:
            if self.running:
                self.log(service, f"日志输出异常: {e}")

    def setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, _frame):
            self.log('system', f"接收到信号 {signum}，正在关闭服务...")
            self.shutdown()

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

    def shutdown(self):
        """关闭所有服务"""
        self.running = False
        self.log('system', "正在关闭所有服务...")

        # 关闭API服务
        if self.api_process and self.api_process.poll() is None:
            self.log('api', "正在关闭API服务...")
            try:
                self.api_process.terminate()
                self.api_process.wait(timeout=10)
                self.log('api', "API服务已关闭")
            except subprocess.TimeoutExpired:
                self.log('api', "强制终止API服务...")
                self.api_process.kill()
                self.api_process.wait()

        # 关闭Celery Worker
        if self.worker_process and self.worker_process.poll() is None:
            self.log('worker', "正在关闭Celery Worker...")
            try:
                self.worker_process.terminate()
                self.worker_process.wait(timeout=10)
                self.log('worker', "Celery Worker已关闭")
            except subprocess.TimeoutExpired:
                self.log('worker', "强制终止Celery Worker...")
                self.worker_process.kill()
                self.worker_process.wait()

        self.log('system', "所有服务已关闭")

    def start_system(self):
        """启动整个系统"""
        print("🚀 AI小说生成器 - VSCode调试启动")
        print("专为VSCode调试环境优化")
        print("=" * 40)

        # 检查环境
        if not self.check_environment():
            return False

        # 设置信号处理器
        self.setup_signal_handlers()

        # 启动API服务
        self.log('system', "正在启动服务...")
        if not self.start_api_server():
            self.log('system', "API服务启动失败")
            return False

        # 等待API服务启动
        time.sleep(3)

        # 启动Celery Worker
        if not self.start_celery_worker():
            self.log('system', "Celery Worker启动失败")
            self.shutdown()
            return False

        # 等待Worker启动
        time.sleep(3)

        self.log('system', "✅ 所有服务启动完成")
        self.log('system', "")
        self.log('system', "服务状态:")
        self.log('system', "  - API服务: http://localhost:8000")
        self.log('system', "  - Swagger文档: http://localhost:8000/docs")
        self.log('system', "  - Celery Worker: 运行中")
        self.log('system', "")
        self.log('system', "在VSCode中按停止按钮或Ctrl+C停止服务")
        self.log('system', "=" * 50)

        # 等待关闭信号
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            self.log('system', "接收到关闭信号...")
            self.shutdown()

        return True


def main():
    """主函数"""
    manager = VSCodeDebugManager()
    success = manager.start_system()

    if not success:
        sys.exit(1)


if __name__ == "__main__":
    main()
