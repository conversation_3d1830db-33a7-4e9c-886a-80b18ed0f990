#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI小说生成器 - VSCode调试专用启动脚本
专门为VSCode调试环境优化
"""
import os
import sys
import subprocess
import threading
import time
import signal
import socket


class VSCodeDebugManager:
    """VSCode调试管理器 - 专门为VSCode调试环境优化"""

    def __init__(self, auto_kill_port=True):
        self.api_process = None
        self.worker_process = None
        self.running = True
        self.auto_kill_port = auto_kill_port  # 是否自动终止占用端口的进程

    def log(self, service, message):
        """统一日志输出"""
        timestamp = time.strftime('%H:%M:%S')
        prefix = f"[{service.upper()}]"
        print(f"{timestamp} {prefix} {message}")

    def check_port_available(self, port):
        """检查端口是否可用"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.settimeout(1)
                result = sock.connect_ex(('127.0.0.1', port))
                return result != 0  # 0表示连接成功，即端口被占用
        except Exception:
            return True  # 出现异常时假设端口可用

    def find_process_using_port(self, port):
        """查找占用端口的进程PID"""
        try:
            if os.name == 'nt':  # Windows
                cmd = f'netstat -ano | findstr :{port}'
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines:
                        if 'LISTENING' in line:
                            parts = line.split()
                            if len(parts) >= 5:
                                pid = int(parts[-1])  # PID是最后一列
                                # 验证进程是否真的存在
                                if self.process_exists(pid):
                                    return pid
            else:  # Unix/Linux/Mac
                cmd = f'lsof -ti:{port}'
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                if result.returncode == 0:
                    pids = result.stdout.strip().split('\n')
                    for pid_str in pids:
                        if pid_str:
                            pid = int(pid_str)
                            if self.process_exists(pid):
                                return pid
        except Exception as e:
            self.log('system', f"查找端口占用进程时出错: {e}")
        return None

    def process_exists(self, pid):
        """检查进程是否存在"""
        try:
            if os.name == 'nt':  # Windows
                result = subprocess.run(f'tasklist /FI "PID eq {pid}"', shell=True, capture_output=True, text=True)
                return str(pid) in result.stdout
            else:  # Unix/Linux/Mac
                result = subprocess.run(f'ps -p {pid}', shell=True, capture_output=True, text=True)
                return result.returncode == 0
        except Exception:
            return False

    def kill_process_by_pid(self, pid):
        """根据PID终止进程"""
        try:
            # 首先检查进程是否真的存在
            if not self.process_exists(pid):
                self.log('system', f"进程 {pid} 不存在，可能是端口缓存问题")
                return False

            if os.name == 'nt':  # Windows
                subprocess.run(f'taskkill /F /PID {pid}', shell=True, check=True)
            else:  # Unix/Linux/Mac
                subprocess.run(f'kill -9 {pid}', shell=True, check=True)
            return True
        except Exception as e:
            self.log('system', f"终止进程 {pid} 时出错: {e}")
            return False

    def force_release_port(self, port):
        """强制释放端口（Windows特有的方法）"""
        if os.name != 'nt':
            return False

        try:
            self.log('system', f"尝试强制释放端口 {port}...")

            # 方法1: 使用netsh重置TCP连接
            subprocess.run(f'netsh int ip reset', shell=True, capture_output=True)
            time.sleep(1)

            # 方法2: 重启网络适配器（需要管理员权限，可能失败）
            try:
                subprocess.run('netsh winsock reset', shell=True, capture_output=True, timeout=5)
                time.sleep(1)
            except:
                pass

            # 检查端口是否释放
            if self.check_port_available(port):
                self.log('system', f"✅ 端口 {port} 已强制释放")
                return True
            else:
                self.log('system', f"❌ 无法强制释放端口 {port}")
                return False

        except Exception as e:
            self.log('system', f"强制释放端口时出错: {e}")
            return False

    def cleanup_port(self, port):
        """清理占用指定端口的进程"""
        self.log('system', f"检查端口 {port} 是否被占用...")

        if self.check_port_available(port):
            self.log('system', f"✅ 端口 {port} 可用")
            return True

        self.log('system', f"⚠️ 端口 {port} 被占用，正在查找占用进程...")

        # 尝试多次查找进程，因为可能存在时间差
        pid = None
        for attempt in range(3):
            pid = self.find_process_using_port(port)
            if pid:
                break
            time.sleep(0.5)  # 等待0.5秒后重试

        if pid:
            self.log('system', f"发现占用进程 PID: {pid}")

            if self.auto_kill_port:
                # 自动模式：直接终止进程
                self.log('system', f"自动模式：正在终止进程 {pid}...")
                if self.kill_process_by_pid(pid):
                    self.log('system', f"✅ 已终止进程 {pid}")

                    # 等待端口释放，最多等待5秒
                    for wait_attempt in range(10):
                        time.sleep(0.5)
                        if self.check_port_available(port):
                            self.log('system', f"✅ 端口 {port} 现在可用")
                            return True

                    self.log('system', f"❌ 端口 {port} 仍被占用")
                    return False
                else:
                    self.log('system', f"❌ 无法终止进程 {pid}")
                    return False
            else:
                # 交互模式：询问用户是否要终止进程
                try:
                    response = input(f"端口 {port} 被进程 {pid} 占用，是否终止该进程？(y/N): ").strip().lower()
                    if response in ['y', 'yes']:
                        if self.kill_process_by_pid(pid):
                            self.log('system', f"✅ 已终止进程 {pid}")

                            # 等待端口释放
                            for wait_attempt in range(10):
                                time.sleep(0.5)
                                if self.check_port_available(port):
                                    self.log('system', f"✅ 端口 {port} 现在可用")
                                    return True

                            self.log('system', f"❌ 端口 {port} 仍被占用")
                            return False
                        else:
                            self.log('system', f"❌ 无法终止进程 {pid}")
                            return False
                    else:
                        self.log('system', "用户选择不终止进程，将尝试使用其他端口")
                        return False
                except KeyboardInterrupt:
                    self.log('system', "用户取消操作")
                    return False
        else:
            # 没有找到进程，但端口仍被占用，可能是系统缓存问题
            self.log('system', f"⚠️ 无法找到占用端口 {port} 的进程，可能是系统缓存问题")

            if self.auto_kill_port and os.name == 'nt':
                # 在Windows上尝试强制释放端口
                if self.force_release_port(port):
                    return True

            self.log('system', f"等待端口自动释放...")

            # 等待一段时间看端口是否自动释放
            for wait_attempt in range(6):  # 等待3秒
                time.sleep(0.5)
                if self.check_port_available(port):
                    self.log('system', f"✅ 端口 {port} 已自动释放")
                    return True

            self.log('system', f"❌ 端口 {port} 仍被占用，将使用其他端口")
            return False

    def check_environment(self):
        """检查运行环境"""
        print("🔍 VSCode调试环境检查...")

        # 检查是否在项目根目录
        if not os.path.exists("app"):
            print("❌ 错误：请在项目根目录运行此脚本")
            return False

        # 检查app/main.py是否存在
        if not os.path.exists("app/main.py"):
            print("❌ 错误：app/main.py 不存在")
            return False

        # 检查app/core/celery_app.py是否存在
        if not os.path.exists("app/core/celery_app.py"):
            print("❌ 错误：app/core/celery_app.py 不存在")
            return False

        print(f"✅ Python版本: {sys.version}")
        print(f"✅ 工作目录: {os.getcwd()}")
        print(f"✅ Python解释器: {sys.executable}")
        
        # 检查调试环境
        if 'debugpy' in sys.modules:
            print("✅ VSCode调试环境已激活")
        else:
            print("⚠️ 未检测到VSCode调试环境")

        return True

    def get_python_executable(self):
        """获取正确的Python解释器路径 - VSCode调试优化版"""
        # 在VSCode调试环境中，直接使用当前解释器
        current_python = os.path.abspath(sys.executable)
        
        # 检查是否在虚拟环境中
        if 'novel_ai_venv' in current_python:
            self.log('system', f"✓ VSCode调试使用虚拟环境: {current_python}")
            return current_python
        
        # 如果不在虚拟环境中，尝试找到虚拟环境
        current_dir = os.getcwd()
        venv_names = ['novel_ai_venv', 'novel_ai_env', 'venv', 'env']
        
        for venv_name in venv_names:
            local_venv = os.path.join(current_dir, venv_name)
            if os.path.exists(local_venv):
                if os.name == 'nt':  # Windows
                    local_python = os.path.join(local_venv, 'Scripts', 'python.exe')
                else:
                    local_python = os.path.join(local_venv, 'bin', 'python')

                if os.path.exists(local_python):
                    local_python = os.path.abspath(local_python)
                    self.log('system', f"✓ 找到虚拟环境Python: {local_python}")
                    return local_python

        self.log('system', f"⚠️ 使用当前Python解释器: {current_python}")
        return current_python

    def start_api_server(self, port=8000):
        """启动API服务器 - VSCode调试优化版"""
        self.log('api', "正在启动API服务器...")

        # 检查并清理端口
        if not self.cleanup_port(port):
            # 如果端口清理失败，尝试使用其他端口
            alternative_ports = [8001, 8002, 8003, 8004, 8005]
            port_found = False

            for alt_port in alternative_ports:
                if self.check_port_available(alt_port):
                    self.log('api', f"使用替代端口: {alt_port}")
                    port = alt_port
                    port_found = True
                    break

            if not port_found:
                self.log('api', "❌ 无法找到可用端口，启动失败")
                return False

        # 设置环境变量
        env = os.environ.copy()
        env['PYTHONPATH'] = os.getcwd()
        env['PYTHONIOENCODING'] = 'utf-8'
        env['PYTHONUNBUFFERED'] = '1'  # 确保输出不被缓冲

        # 获取正确的Python解释器
        python_exe = self.get_python_executable()

        # 构建启动命令
        cmd = [
            python_exe, "-m", "uvicorn",
            "app.main:app",
            "--host=0.0.0.0",
            f"--port={port}",
            "--reload",
            "--log-level=info"
        ]

        self.log('api', f"执行命令: {' '.join(cmd)}")

        try:
            # 启动API服务
            self.api_process = subprocess.Popen(
                cmd,
                cwd=os.getcwd(),
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=0,  # 无缓冲
                encoding='utf-8',
                errors='replace',
                env=env
            )
            # 保存实际使用的端口
            self.api_port = str(port)

            self.log('api', f"API服务已启动，PID: {self.api_process.pid}")
            self.log('api', f"服务地址: http://localhost:{port}")
            self.log('api', f"API文档: http://localhost:{port}/docs")

            # 启动日志输出线程
            threading.Thread(
                target=self.log_output_thread,
                args=(self.api_process, 'api'),
                daemon=True
            ).start()

            return True

        except Exception as e:
            self.log('api', f"启动失败: {e}")
            return False

    def start_celery_worker(self):
        """启动Celery Worker - VSCode调试优化版"""
        self.log('worker', "正在启动Celery Worker...")

        # 设置环境变量
        env = os.environ.copy()
        env['PYTHONPATH'] = os.getcwd()
        env['PYTHONIOENCODING'] = 'utf-8'
        env['PYTHONUNBUFFERED'] = '1'  # 确保输出不被缓冲

        # 获取正确的Python解释器
        python_exe = self.get_python_executable()

        # 构建启动命令
        cmd = [
            python_exe, "-m", "celery",
            "-A", "app.core.celery_app:celery_app",
            "worker",
            "--loglevel=info",
            "--pool=solo"
        ]

        self.log('worker', f"执行命令: {' '.join(cmd)}")

        try:
            # 启动Celery Worker
            self.worker_process = subprocess.Popen(
                cmd,
                cwd=os.getcwd(),
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=0,  # 无缓冲
                encoding='utf-8',
                errors='replace',
                env=env
            )
            self.log('worker', f"Celery Worker已启动，PID: {self.worker_process.pid}")

            # 启动日志输出线程
            threading.Thread(
                target=self.log_output_thread,
                args=(self.worker_process, 'worker'),
                daemon=True
            ).start()

            return True

        except Exception as e:
            self.log('worker', f"启动失败: {e}")
            return False

    def log_output_thread(self, process, service):
        """日志输出线程 - VSCode调试优化版"""
        try:
            for line in iter(process.stdout.readline, ''):
                if line.strip() and self.running:
                    try:
                        clean_line = line.strip()
                        if clean_line:
                            self.log(service, clean_line)
                            # 强制刷新输出，确保在VSCode中可见
                            sys.stdout.flush()
                    except UnicodeError:
                        safe_line = line.strip().encode('utf-8', errors='ignore').decode('utf-8')
                        if safe_line:
                            self.log(service, safe_line)
                            sys.stdout.flush()
        except Exception as e:
            if self.running:
                self.log(service, f"日志输出异常: {e}")

    def setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, _frame):
            self.log('system', f"接收到信号 {signum}，正在关闭服务...")
            self.shutdown()

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

    def shutdown(self):
        """关闭所有服务"""
        self.running = False
        self.log('system', "正在关闭所有服务...")

        # 关闭API服务
        if self.api_process and self.api_process.poll() is None:
            self.log('api', "正在关闭API服务...")
            try:
                self.api_process.terminate()
                self.api_process.wait(timeout=10)
                self.log('api', "API服务已关闭")
            except subprocess.TimeoutExpired:
                self.log('api', "强制终止API服务...")
                self.api_process.kill()
                self.api_process.wait()

        # 关闭Celery Worker
        if self.worker_process and self.worker_process.poll() is None:
            self.log('worker', "正在关闭Celery Worker...")
            try:
                self.worker_process.terminate()
                self.worker_process.wait(timeout=10)
                self.log('worker', "Celery Worker已关闭")
            except subprocess.TimeoutExpired:
                self.log('worker', "强制终止Celery Worker...")
                self.worker_process.kill()
                self.worker_process.wait()

        self.log('system', "所有服务已关闭")

    def start_system(self):
        """启动整个系统"""
        print("🚀 AI小说生成器 - VSCode调试启动")
        print("专为VSCode调试环境优化")
        print("包含自动端口清理功能")
        print("=" * 40)

        # 检查环境
        if not self.check_environment():
            return False

        # 设置信号处理器
        self.setup_signal_handlers()

        # 启动API服务（包含端口清理）
        self.log('system', "正在启动服务...")
        if not self.start_api_server():
            self.log('system', "API服务启动失败")
            return False

        # 等待API服务启动
        time.sleep(3)

        # 启动Celery Worker
        if not self.start_celery_worker():
            self.log('system', "Celery Worker启动失败")
            self.shutdown()
            return False

        # 等待Worker启动
        time.sleep(3)

        self.log('system', "✅ 所有服务启动完成")
        self.log('system', "")
        self.log('system', "服务状态:")

        # 动态显示实际使用的端口
        api_port = self.get_api_port()
        self.log('system', f"  - API服务: http://localhost:{api_port}")
        self.log('system', f"  - Swagger文档: http://localhost:{api_port}/docs")
        self.log('system', "  - Celery Worker: 运行中")
        self.log('system', "")
        self.log('system', "在VSCode中按停止按钮或Ctrl+C停止服务")
        self.log('system', "=" * 50)

        # 等待关闭信号
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            self.log('system', "接收到关闭信号...")
            self.shutdown()

        return True

    def get_api_port(self):
        """获取API服务实际使用的端口"""
        # 简单方法：从实例变量中获取端口（需要在start_api_server中设置）
        return getattr(self, 'api_port', '8000')


def main():
    """主函数"""
    # 检查命令行参数
    auto_kill = True  # 默认启用自动端口清理

    if len(sys.argv) > 1:
        if '--no-auto-kill' in sys.argv:
            auto_kill = False
        elif '--help' in sys.argv or '-h' in sys.argv:
            print("AI小说生成器 - VSCode调试启动脚本")
            print("用法: python start_vscode_debug.py [选项]")
            print("选项:")
            print("  --no-auto-kill    禁用自动端口清理，改为交互模式")
            print("  --help, -h        显示此帮助信息")
            return

    manager = VSCodeDebugManager(auto_kill_port=auto_kill)

    if auto_kill:
        print("🔧 自动端口清理模式已启用")
        print("   如需禁用，请使用参数: --no-auto-kill")
    else:
        print("🔧 交互端口清理模式已启用")

    success = manager.start_system()

    if not success:
        sys.exit(1)


if __name__ == "__main__":
    main()
