{"tables": {"users": {"status": "PASS", "message": "", "columns": {"id": {"status": "PASS", "issues": [], "expected": {"type": "INTEGER", "nullable": false, "primary_key": true}, "actual": {"name": "id", "type": "INTEGER", "nullable": false, "default": "nextval('users_id_seq'::regclass)", "autoincrement": true, "comment": null}}, "wx_openid": {"status": "PASS", "issues": [], "expected": {"type": "VARCHAR", "nullable": false, "unique": true, "length": 128}, "actual": {"name": "wx_openid", "type": "VARCHAR(128)", "nullable": false, "default": null, "autoincrement": false, "comment": "微信openid"}}, "nickname": {"status": "PASS", "issues": [], "expected": {"type": "VARCHAR", "nullable": true, "length": 255}, "actual": {"name": "nickname", "type": "VARCHAR(255)", "nullable": true, "default": null, "autoincrement": false, "comment": "用户昵称"}}, "avatar_url": {"status": "PASS", "issues": [], "expected": {"type": "VARCHAR", "nullable": true, "length": 500}, "actual": {"name": "avatar_url", "type": "VARCHAR(500)", "nullable": true, "default": null, "autoincrement": false, "comment": "用户头像URL"}}, "quota_used": {"status": "PASS", "issues": [], "expected": {"type": "INTEGER", "nullable": true, "default": 0}, "actual": {"name": "quota_used", "type": "INTEGER", "nullable": true, "default": null, "autoincrement": false, "comment": "已使用配额"}}, "quota_limit": {"status": "PASS", "issues": [], "expected": {"type": "INTEGER", "nullable": true, "default": 10}, "actual": {"name": "quota_limit", "type": "INTEGER", "nullable": true, "default": null, "autoincrement": false, "comment": "配额限制"}}, "is_vip": {"status": "PASS", "issues": [], "expected": {"type": "BOOLEAN", "nullable": true, "default": false}, "actual": {"name": "is_vip", "type": "BOOLEAN", "nullable": true, "default": null, "autoincrement": false, "comment": "是否VIP用户"}}, "settings": {"status": "PASS", "issues": [], "expected": {"type": "JSON", "nullable": true}, "actual": {"name": "settings", "type": "JSON", "nullable": true, "default": null, "autoincrement": false, "comment": "用户设置JSON"}}, "created_at": {"status": "PASS", "issues": [], "expected": {"type": "TIMESTAMP", "nullable": true}, "actual": {"name": "created_at", "type": "TIMESTAMP", "nullable": true, "default": "now()", "autoincrement": false, "comment": "创建时间"}}, "updated_at": {"status": "PASS", "issues": [], "expected": {"type": "TIMESTAMP", "nullable": true}, "actual": {"name": "updated_at", "type": "TIMESTAMP", "nullable": true, "default": "now()", "autoincrement": false, "comment": "更新时间"}}}, "indexes": {"ix_users_id": {"status": "PASS"}, "ix_users_wx_openid": {"status": "PASS"}}, "constraints": {}, "foreign_keys": {}}, "novels": {"status": "PASS", "message": "", "columns": {"id": {"status": "PASS", "issues": [], "expected": {"type": "INTEGER", "nullable": false, "primary_key": true}, "actual": {"name": "id", "type": "INTEGER", "nullable": false, "default": "nextval('novels_id_seq'::regclass)", "autoincrement": true, "comment": null}}, "user_id": {"status": "PASS", "issues": [], "expected": {"type": "INTEGER", "nullable": false, "foreign_key": "users.id"}, "actual": {"name": "user_id", "type": "INTEGER", "nullable": false, "default": null, "autoincrement": false, "comment": "用户ID"}}, "title": {"status": "PASS", "issues": [], "expected": {"type": "VARCHAR", "nullable": false, "length": 255}, "actual": {"name": "title", "type": "VARCHAR(255)", "nullable": false, "default": null, "autoincrement": false, "comment": "小说标题"}}, "description": {"status": "PASS", "issues": [], "expected": {"type": "TEXT", "nullable": true}, "actual": {"name": "description", "type": "TEXT", "nullable": true, "default": null, "autoincrement": false, "comment": null}}, "genre": {"status": "PASS", "issues": [], "expected": {"type": "VARCHAR", "nullable": true, "length": 50}, "actual": {"name": "genre", "type": "VARCHAR(50)", "nullable": true, "default": null, "autoincrement": false, "comment": "小说类型"}}, "target_length": {"status": "PASS", "issues": [], "expected": {"type": "INTEGER", "nullable": true}, "actual": {"name": "target_length", "type": "INTEGER", "nullable": true, "default": null, "autoincrement": false, "comment": null}}, "style_settings": {"status": "PASS", "issues": [], "expected": {"type": "JSON", "nullable": true}, "actual": {"name": "style_settings", "type": "JSONB", "nullable": true, "default": null, "autoincrement": false, "comment": null}}, "status": {"status": "PASS", "issues": [], "expected": {"type": "ENUM", "nullable": true}, "actual": {"name": "status", "type": "VARCHAR(10)", "nullable": true, "default": null, "autoincrement": false, "comment": "小说状态"}}, "config": {"status": "PASS", "issues": [], "expected": {"type": "JSON", "nullable": true}, "actual": {"name": "config", "type": "JSON", "nullable": true, "default": null, "autoincrement": false, "comment": "小说配置JSON"}}, "chapter_count": {"status": "PASS", "issues": [], "expected": {"type": "INTEGER", "nullable": true, "default": 0}, "actual": {"name": "chapter_count", "type": "INTEGER", "nullable": true, "default": null, "autoincrement": false, "comment": "章节数量"}}, "total_words": {"status": "PASS", "issues": [], "expected": {"type": "INTEGER", "nullable": true, "default": 0}, "actual": {"name": "total_words", "type": "INTEGER", "nullable": true, "default": null, "autoincrement": false, "comment": "总字数"}}, "created_at": {"status": "PASS", "issues": [], "expected": {"type": "TIMESTAMP", "nullable": true}, "actual": {"name": "created_at", "type": "TIMESTAMP", "nullable": true, "default": "now()", "autoincrement": false, "comment": "创建时间"}}, "updated_at": {"status": "PASS", "issues": [], "expected": {"type": "TIMESTAMP", "nullable": true}, "actual": {"name": "updated_at", "type": "TIMESTAMP", "nullable": true, "default": "now()", "autoincrement": false, "comment": "更新时间"}}}, "indexes": {"ix_novels_id": {"status": "PASS"}, "ix_novels_user_id": {"status": "PASS"}}, "constraints": {}, "foreign_keys": {"novels_user_id_fkey": {"status": "PASS"}}}, "chapters": {"status": "PASS", "message": "", "columns": {"id": {"status": "PASS", "issues": [], "expected": {"type": "INTEGER", "nullable": false, "primary_key": true}, "actual": {"name": "id", "type": "INTEGER", "nullable": false, "default": "nextval('chapters_id_seq'::regclass)", "autoincrement": true, "comment": null}}, "novel_id": {"status": "PASS", "issues": [], "expected": {"type": "INTEGER", "nullable": false, "foreign_key": "novels.id"}, "actual": {"name": "novel_id", "type": "INTEGER", "nullable": false, "default": null, "autoincrement": false, "comment": "小说ID"}}, "chapter_number": {"status": "PASS", "issues": [], "expected": {"type": "INTEGER", "nullable": false}, "actual": {"name": "chapter_number", "type": "INTEGER", "nullable": false, "default": null, "autoincrement": false, "comment": "章节编号"}}, "title": {"status": "PASS", "issues": [], "expected": {"type": "VARCHAR", "nullable": true, "length": 255}, "actual": {"name": "title", "type": "VARCHAR(255)", "nullable": true, "default": null, "autoincrement": false, "comment": "章节标题"}}, "content": {"status": "PASS", "issues": [], "expected": {"type": "TEXT", "nullable": true}, "actual": {"name": "content", "type": "TEXT", "nullable": true, "default": null, "autoincrement": false, "comment": "章节内容"}}, "summary": {"status": "PASS", "issues": [], "expected": {"type": "TEXT", "nullable": true}, "actual": {"name": "summary", "type": "TEXT", "nullable": true, "default": null, "autoincrement": false, "comment": null}}, "status": {"status": "PASS", "issues": [], "expected": {"type": "ENUM", "nullable": true}, "actual": {"name": "status", "type": "VARCHAR(9)", "nullable": true, "default": null, "autoincrement": false, "comment": "章节状态"}}, "word_count": {"status": "PASS", "issues": [], "expected": {"type": "INTEGER", "nullable": true, "default": 0}, "actual": {"name": "word_count", "type": "INTEGER", "nullable": true, "default": null, "autoincrement": false, "comment": "字数统计"}}, "version": {"status": "PASS", "issues": [], "expected": {"type": "INTEGER", "nullable": true, "default": 1}, "actual": {"name": "version", "type": "INTEGER", "nullable": true, "default": null, "autoincrement": false, "comment": "版本号"}}, "parent_id": {"status": "PASS", "issues": [], "expected": {"type": "INTEGER", "nullable": true, "foreign_key": "chapters.id"}, "actual": {"name": "parent_id", "type": "INTEGER", "nullable": true, "default": null, "autoincrement": false, "comment": "父版本ID"}}, "is_active": {"status": "PASS", "issues": [], "expected": {"type": "BOOLEAN", "nullable": true, "default": true}, "actual": {"name": "is_active", "type": "BOOLEAN", "nullable": true, "default": null, "autoincrement": false, "comment": "是否为当前活跃版本"}}, "created_at": {"status": "PASS", "issues": [], "expected": {"type": "TIMESTAMP", "nullable": true}, "actual": {"name": "created_at", "type": "TIMESTAMP", "nullable": true, "default": "now()", "autoincrement": false, "comment": "创建时间"}}, "updated_at": {"status": "PASS", "issues": [], "expected": {"type": "TIMESTAMP", "nullable": true}, "actual": {"name": "updated_at", "type": "TIMESTAMP", "nullable": true, "default": "now()", "autoincrement": false, "comment": "更新时间"}}}, "indexes": {"ix_chapters_id": {"status": "PASS"}, "ix_chapters_novel_id": {"status": "PASS"}}, "constraints": {}, "foreign_keys": {"chapters_novel_id_fkey": {"status": "PASS"}, "chapters_parent_id_fkey": {"status": "PASS"}}}, "documents": {"status": "PASS", "message": "", "columns": {"id": {"status": "PASS", "issues": [], "expected": {"type": "INTEGER", "nullable": false, "primary_key": true}, "actual": {"name": "id", "type": "INTEGER", "nullable": false, "default": "nextval('documents_id_seq'::regclass)", "autoincrement": true, "comment": null}}, "novel_id": {"status": "PASS", "issues": [], "expected": {"type": "INTEGER", "nullable": false, "foreign_key": "novels.id"}, "actual": {"name": "novel_id", "type": "INTEGER", "nullable": false, "default": null, "autoincrement": false, "comment": "小说ID"}}, "doc_type": {"status": "PASS", "issues": [], "expected": {"type": "ENUM", "nullable": false}, "actual": {"name": "doc_type", "type": "VARCHAR(15)", "nullable": false, "default": null, "autoincrement": false, "comment": "文档类型"}}, "title": {"status": "PASS", "issues": [], "expected": {"type": "VARCHAR", "nullable": false, "length": 200}, "actual": {"name": "title", "type": "VARCHAR(200)", "nullable": false, "default": "'未命名文档'::character varying", "autoincrement": false, "comment": null}}, "content": {"status": "PASS", "issues": [], "expected": {"type": "TEXT", "nullable": true}, "actual": {"name": "content", "type": "TEXT", "nullable": true, "default": null, "autoincrement": false, "comment": "文档内容"}}, "version": {"status": "PASS", "issues": [], "expected": {"type": "INTEGER", "nullable": true, "default": 1}, "actual": {"name": "version", "type": "INTEGER", "nullable": true, "default": null, "autoincrement": false, "comment": "版本号"}}, "parent_id": {"status": "PASS", "issues": [], "expected": {"type": "INTEGER", "nullable": true, "foreign_key": "documents.id"}, "actual": {"name": "parent_id", "type": "INTEGER", "nullable": true, "default": null, "autoincrement": false, "comment": "父版本ID"}}, "is_active": {"status": "PASS", "issues": [], "expected": {"type": "BOOLEAN", "nullable": true, "default": true}, "actual": {"name": "is_active", "type": "BOOLEAN", "nullable": true, "default": null, "autoincrement": false, "comment": "是否为当前活跃版本"}}, "summary_level": {"status": "PASS", "issues": [], "expected": {"type": "ENUM", "nullable": true}, "actual": {"name": "summary_level", "type": "VARCHAR(20)", "nullable": true, "default": null, "autoincrement": false, "comment": null}}, "chapter_range_start": {"status": "PASS", "issues": [], "expected": {"type": "INTEGER", "nullable": true}, "actual": {"name": "chapter_range_start", "type": "INTEGER", "nullable": true, "default": null, "autoincrement": false, "comment": null}}, "chapter_range_end": {"status": "PASS", "issues": [], "expected": {"type": "INTEGER", "nullable": true}, "actual": {"name": "chapter_range_end", "type": "INTEGER", "nullable": true, "default": null, "autoincrement": false, "comment": null}}, "created_at": {"status": "PASS", "issues": [], "expected": {"type": "TIMESTAMP", "nullable": true}, "actual": {"name": "created_at", "type": "TIMESTAMP", "nullable": true, "default": "now()", "autoincrement": false, "comment": "创建时间"}}, "updated_at": {"status": "PASS", "issues": [], "expected": {"type": "TIMESTAMP", "nullable": true}, "actual": {"name": "updated_at", "type": "TIMESTAMP", "nullable": true, "default": "now()", "autoincrement": false, "comment": "更新时间"}}}, "indexes": {"ix_documents_id": {"status": "PASS"}, "ix_documents_novel_id": {"status": "PASS"}}, "constraints": {}, "foreign_keys": {"documents_novel_id_fkey": {"status": "PASS"}, "documents_parent_id_fkey": {"status": "PASS"}}}, "generation_tasks": {"status": "PASS", "message": "", "columns": {"id": {"status": "PASS", "issues": [], "expected": {"type": "UUID", "nullable": false, "primary_key": true}, "actual": {"name": "id", "type": "UUID", "nullable": false, "default": null, "autoincrement": false, "comment": null}}, "task_id": {"status": "PASS", "issues": [], "expected": {"type": "VARCHAR", "nullable": false, "unique": true, "length": 255}, "actual": {"name": "task_id", "type": "VARCHAR(255)", "nullable": false, "default": null, "autoincrement": false, "comment": null}}, "user_id": {"status": "PASS", "issues": [], "expected": {"type": "INTEGER", "nullable": false, "foreign_key": "users.id"}, "actual": {"name": "user_id", "type": "INTEGER", "nullable": false, "default": null, "autoincrement": false, "comment": "用户ID"}}, "novel_id": {"status": "PASS", "issues": [], "expected": {"type": "INTEGER", "nullable": true, "foreign_key": "novels.id"}, "actual": {"name": "novel_id", "type": "INTEGER", "nullable": true, "default": null, "autoincrement": false, "comment": "小说ID"}}, "task_type": {"status": "PASS", "issues": [], "expected": {"type": "ENUM", "nullable": false}, "actual": {"name": "task_type", "type": "VARCHAR(21)", "nullable": false, "default": null, "autoincrement": false, "comment": "任务类型"}}, "status": {"status": "PASS", "issues": [], "expected": {"type": "ENUM", "nullable": true}, "actual": {"name": "status", "type": "VARCHAR(11)", "nullable": true, "default": null, "autoincrement": false, "comment": "任务状态"}}, "progress": {"status": "PASS", "issues": [], "expected": {"type": "INTEGER", "nullable": true, "default": 0}, "actual": {"name": "progress", "type": "INTEGER", "nullable": true, "default": null, "autoincrement": false, "comment": "进度百分比(0-100)"}}, "parameters": {"status": "PASS", "issues": [], "expected": {"type": "TEXT", "nullable": true}, "actual": {"name": "parameters", "type": "TEXT", "nullable": true, "default": null, "autoincrement": false, "comment": "任务参数JSON"}}, "result_doc_id": {"status": "PASS", "issues": [], "expected": {"type": "INTEGER", "nullable": true}, "actual": {"name": "result_doc_id", "type": "INTEGER", "nullable": true, "default": null, "autoincrement": false, "comment": "结果文档ID"}}, "result_chapter_id": {"status": "PASS", "issues": [], "expected": {"type": "INTEGER", "nullable": true}, "actual": {"name": "result_chapter_id", "type": "INTEGER", "nullable": true, "default": null, "autoincrement": false, "comment": "结果章节ID"}}, "error_message": {"status": "PASS", "issues": [], "expected": {"type": "TEXT", "nullable": true}, "actual": {"name": "error_message", "type": "TEXT", "nullable": true, "default": null, "autoincrement": false, "comment": "错误信息"}}, "created_at": {"status": "PASS", "issues": [], "expected": {"type": "TIMESTAMP", "nullable": true}, "actual": {"name": "created_at", "type": "TIMESTAMP", "nullable": true, "default": "now()", "autoincrement": false, "comment": "创建时间"}}, "updated_at": {"status": "PASS", "issues": [], "expected": {"type": "TIMESTAMP", "nullable": true}, "actual": {"name": "updated_at", "type": "TIMESTAMP", "nullable": true, "default": "now()", "autoincrement": false, "comment": "更新时间"}}, "started_at": {"status": "PASS", "issues": [], "expected": {"type": "TIMESTAMP", "nullable": true}, "actual": {"name": "started_at", "type": "TIMESTAMP", "nullable": true, "default": null, "autoincrement": false, "comment": "开始时间"}}, "completed_at": {"status": "PASS", "issues": [], "expected": {"type": "TIMESTAMP", "nullable": true}, "actual": {"name": "completed_at", "type": "TIMESTAMP", "nullable": true, "default": null, "autoincrement": false, "comment": "完成时间"}}}, "indexes": {"ix_generation_tasks_id": {"status": "PASS"}, "ix_generation_tasks_task_id": {"status": "PASS"}, "ix_generation_tasks_user_id": {"status": "PASS"}, "ix_generation_tasks_novel_id": {"status": "PASS"}}, "constraints": {}, "foreign_keys": {"generation_tasks_user_id_fkey": {"status": "PASS"}, "generation_tasks_novel_id_fkey": {"status": "PASS"}}}, "generation_states": {"status": "PASS", "message": "", "columns": {"id": {"status": "PASS", "issues": [], "expected": {"type": "INTEGER", "nullable": false, "primary_key": true}, "actual": {"name": "id", "type": "INTEGER", "nullable": false, "default": "nextval('generation_states_id_seq'::regclass)", "autoincrement": true, "comment": null}}, "novel_id": {"status": "PASS", "issues": [], "expected": {"type": "INTEGER", "nullable": false}, "actual": {"name": "novel_id", "type": "INTEGER", "nullable": false, "default": null, "autoincrement": false, "comment": null}}, "task_id": {"status": "PASS", "issues": [], "expected": {"type": "VARCHAR", "nullable": false}, "actual": {"name": "task_id", "type": "VARCHAR", "nullable": false, "default": null, "autoincrement": false, "comment": null}}, "generation_type": {"status": "PASS", "issues": [], "expected": {"type": "VARCHAR", "nullable": false}, "actual": {"name": "generation_type", "type": "VARCHAR", "nullable": false, "default": null, "autoincrement": false, "comment": null}}, "current_step": {"status": "PASS", "issues": [], "expected": {"type": "VARCHAR", "nullable": false}, "actual": {"name": "current_step", "type": "VARCHAR", "nullable": false, "default": null, "autoincrement": false, "comment": null}}, "completed_steps": {"status": "PASS", "issues": [], "expected": {"type": "JSON", "nullable": true}, "actual": {"name": "completed_steps", "type": "JSON", "nullable": true, "default": null, "autoincrement": false, "comment": null}}, "step_progress": {"status": "PASS", "issues": [], "expected": {"type": "INTEGER", "nullable": true, "default": 0}, "actual": {"name": "step_progress", "type": "INTEGER", "nullable": true, "default": null, "autoincrement": false, "comment": null}}, "total_progress": {"status": "PASS", "issues": [], "expected": {"type": "INTEGER", "nullable": true, "default": 0}, "actual": {"name": "total_progress", "type": "INTEGER", "nullable": true, "default": null, "autoincrement": false, "comment": null}}, "is_completed": {"status": "PASS", "issues": [], "expected": {"type": "BOOLEAN", "nullable": true, "default": false}, "actual": {"name": "is_completed", "type": "BOOLEAN", "nullable": true, "default": null, "autoincrement": false, "comment": null}}, "is_failed": {"status": "PASS", "issues": [], "expected": {"type": "BOOLEAN", "nullable": true, "default": false}, "actual": {"name": "is_failed", "type": "BOOLEAN", "nullable": true, "default": null, "autoincrement": false, "comment": null}}, "error_message": {"status": "PASS", "issues": [], "expected": {"type": "TEXT", "nullable": true}, "actual": {"name": "error_message", "type": "TEXT", "nullable": true, "default": null, "autoincrement": false, "comment": null}}, "created_at": {"status": "PASS", "issues": [], "expected": {"type": "TIMESTAMP", "nullable": true}, "actual": {"name": "created_at", "type": "TIMESTAMP", "nullable": true, "default": "now()", "autoincrement": false, "comment": null}}, "updated_at": {"status": "PASS", "issues": [], "expected": {"type": "TIMESTAMP", "nullable": true}, "actual": {"name": "updated_at", "type": "TIMESTAMP", "nullable": true, "default": null, "autoincrement": false, "comment": null}}, "completed_at": {"status": "PASS", "issues": [], "expected": {"type": "TIMESTAMP", "nullable": true}, "actual": {"name": "completed_at", "type": "TIMESTAMP", "nullable": true, "default": null, "autoincrement": false, "comment": null}}}, "indexes": {"ix_generation_states_id": {"status": "PASS"}, "ix_generation_states_novel_id": {"status": "PASS"}, "ix_generation_states_task_id": {"status": "PASS"}}, "constraints": {}, "foreign_keys": {}}, "llm_configs": {"status": "PASS", "message": "", "columns": {"id": {"status": "PASS", "issues": [], "expected": {"type": "INTEGER", "nullable": false, "primary_key": true}, "actual": {"name": "id", "type": "INTEGER", "nullable": false, "default": "nextval('llm_configs_id_seq'::regclass)", "autoincrement": true, "comment": null}}, "user_id": {"status": "PASS", "issues": [], "expected": {"type": "INTEGER", "nullable": false, "foreign_key": "users.id"}, "actual": {"name": "user_id", "type": "INTEGER", "nullable": false, "default": null, "autoincrement": false, "comment": null}}, "name": {"status": "PASS", "issues": [], "expected": {"type": "VARCHAR", "nullable": false, "length": 255}, "actual": {"name": "name", "type": "VARCHAR(255)", "nullable": false, "default": null, "autoincrement": false, "comment": null}}, "provider": {"status": "PASS", "issues": [], "expected": {"type": "VARCHAR", "nullable": false, "length": 50}, "actual": {"name": "provider", "type": "VARCHAR(50)", "nullable": false, "default": null, "autoincrement": false, "comment": null}}, "is_default": {"status": "PASS", "issues": [], "expected": {"type": "BOOLEAN", "nullable": true, "default": false}, "actual": {"name": "is_default", "type": "BOOLEAN", "nullable": true, "default": null, "autoincrement": false, "comment": null}}, "is_active": {"status": "PASS", "issues": [], "expected": {"type": "BOOLEAN", "nullable": true, "default": true}, "actual": {"name": "is_active", "type": "BOOLEAN", "nullable": true, "default": null, "autoincrement": false, "comment": null}}, "api_key": {"status": "PASS", "issues": [], "expected": {"type": "VARCHAR", "nullable": true, "length": 500}, "actual": {"name": "api_key", "type": "VARCHAR(500)", "nullable": true, "default": null, "autoincrement": false, "comment": null}}, "base_url": {"status": "PASS", "issues": [], "expected": {"type": "VARCHAR", "nullable": true, "length": 500}, "actual": {"name": "base_url", "type": "VARCHAR(500)", "nullable": true, "default": null, "autoincrement": false, "comment": null}}, "api_version": {"status": "PASS", "issues": [], "expected": {"type": "VARCHAR", "nullable": true, "length": 50}, "actual": {"name": "api_version", "type": "VARCHAR(50)", "nullable": true, "default": null, "autoincrement": false, "comment": null}}, "model": {"status": "PASS", "issues": [], "expected": {"type": "VARCHAR", "nullable": false, "length": 100}, "actual": {"name": "model", "type": "VARCHAR(100)", "nullable": false, "default": null, "autoincrement": false, "comment": null}}, "temperature": {"status": "PASS", "issues": [], "expected": {"type": "FLOAT", "nullable": true, "default": 0.7}, "actual": {"name": "temperature", "type": "DOUBLE PRECISION", "nullable": true, "default": null, "autoincrement": false, "comment": null}}, "max_tokens": {"status": "PASS", "issues": [], "expected": {"type": "INTEGER", "nullable": true, "default": 2048}, "actual": {"name": "max_tokens", "type": "INTEGER", "nullable": true, "default": null, "autoincrement": false, "comment": null}}, "top_p": {"status": "PASS", "issues": [], "expected": {"type": "FLOAT", "nullable": true, "default": 1.0}, "actual": {"name": "top_p", "type": "DOUBLE PRECISION", "nullable": true, "default": null, "autoincrement": false, "comment": null}}, "frequency_penalty": {"status": "PASS", "issues": [], "expected": {"type": "FLOAT", "nullable": true, "default": 0.0}, "actual": {"name": "frequency_penalty", "type": "DOUBLE PRECISION", "nullable": true, "default": null, "autoincrement": false, "comment": null}}, "presence_penalty": {"status": "PASS", "issues": [], "expected": {"type": "FLOAT", "nullable": true, "default": 0.0}, "actual": {"name": "presence_penalty", "type": "DOUBLE PRECISION", "nullable": true, "default": null, "autoincrement": false, "comment": null}}, "timeout": {"status": "PASS", "issues": [], "expected": {"type": "INTEGER", "nullable": true, "default": 60}, "actual": {"name": "timeout", "type": "INTEGER", "nullable": true, "default": null, "autoincrement": false, "comment": null}}, "max_retries": {"status": "PASS", "issues": [], "expected": {"type": "INTEGER", "nullable": true, "default": 3}, "actual": {"name": "max_retries", "type": "INTEGER", "nullable": true, "default": null, "autoincrement": false, "comment": null}}, "retry_delay": {"status": "PASS", "issues": [], "expected": {"type": "FLOAT", "nullable": true, "default": 1.0}, "actual": {"name": "retry_delay", "type": "DOUBLE PRECISION", "nullable": true, "default": null, "autoincrement": false, "comment": null}}, "custom_headers": {"status": "PASS", "issues": [], "expected": {"type": "JSON", "nullable": true}, "actual": {"name": "custom_headers", "type": "JSON", "nullable": true, "default": null, "autoincrement": false, "comment": null}}, "custom_params": {"status": "PASS", "issues": [], "expected": {"type": "JSON", "nullable": true}, "actual": {"name": "custom_params", "type": "JSON", "nullable": true, "default": null, "autoincrement": false, "comment": null}}, "total_requests": {"status": "PASS", "issues": [], "expected": {"type": "INTEGER", "nullable": true, "default": 0}, "actual": {"name": "total_requests", "type": "INTEGER", "nullable": true, "default": null, "autoincrement": false, "comment": null}}, "total_tokens": {"status": "PASS", "issues": [], "expected": {"type": "INTEGER", "nullable": true, "default": 0}, "actual": {"name": "total_tokens", "type": "INTEGER", "nullable": true, "default": null, "autoincrement": false, "comment": null}}, "last_used_at": {"status": "PASS", "issues": [], "expected": {"type": "TIMESTAMP", "nullable": true}, "actual": {"name": "last_used_at", "type": "TIMESTAMP", "nullable": true, "default": null, "autoincrement": false, "comment": null}}, "created_at": {"status": "PASS", "issues": [], "expected": {"type": "TIMESTAMP", "nullable": true}, "actual": {"name": "created_at", "type": "TIMESTAMP", "nullable": true, "default": "now()", "autoincrement": false, "comment": null}}, "updated_at": {"status": "PASS", "issues": [], "expected": {"type": "TIMESTAMP", "nullable": true}, "actual": {"name": "updated_at", "type": "TIMESTAMP", "nullable": true, "default": "now()", "autoincrement": false, "comment": null}}}, "indexes": {"ix_llm_configs_id": {"status": "PASS"}}, "constraints": {}, "foreign_keys": {"llm_configs_user_id_fkey": {"status": "PASS"}}}, "llm_usage_logs": {"status": "PASS", "message": "", "columns": {"id": {"status": "PASS", "issues": [], "expected": {"type": "INTEGER", "nullable": false, "primary_key": true}, "actual": {"name": "id", "type": "INTEGER", "nullable": false, "default": "nextval('llm_usage_logs_id_seq'::regclass)", "autoincrement": true, "comment": null}}, "user_id": {"status": "PASS", "issues": [], "expected": {"type": "INTEGER", "nullable": false, "foreign_key": "users.id"}, "actual": {"name": "user_id", "type": "INTEGER", "nullable": false, "default": null, "autoincrement": false, "comment": null}}, "config_id": {"status": "PASS", "issues": [], "expected": {"type": "INTEGER", "nullable": false, "foreign_key": "llm_configs.id"}, "actual": {"name": "config_id", "type": "INTEGER", "nullable": false, "default": null, "autoincrement": false, "comment": null}}, "request_id": {"status": "PASS", "issues": [], "expected": {"type": "VARCHAR", "nullable": true, "length": 100}, "actual": {"name": "request_id", "type": "VARCHAR(100)", "nullable": true, "default": null, "autoincrement": false, "comment": null}}, "endpoint": {"status": "PASS", "issues": [], "expected": {"type": "VARCHAR", "nullable": true, "length": 200}, "actual": {"name": "endpoint", "type": "VARCHAR(200)", "nullable": true, "default": null, "autoincrement": false, "comment": null}}, "method": {"status": "PASS", "issues": [], "expected": {"type": "VARCHAR", "nullable": true, "length": 10}, "actual": {"name": "method", "type": "VARCHAR(10)", "nullable": true, "default": null, "autoincrement": false, "comment": null}}, "prompt_tokens": {"status": "PASS", "issues": [], "expected": {"type": "INTEGER", "nullable": true, "default": 0}, "actual": {"name": "prompt_tokens", "type": "INTEGER", "nullable": true, "default": null, "autoincrement": false, "comment": null}}, "completion_tokens": {"status": "PASS", "issues": [], "expected": {"type": "INTEGER", "nullable": true, "default": 0}, "actual": {"name": "completion_tokens", "type": "INTEGER", "nullable": true, "default": null, "autoincrement": false, "comment": null}}, "total_tokens": {"status": "PASS", "issues": [], "expected": {"type": "INTEGER", "nullable": true, "default": 0}, "actual": {"name": "total_tokens", "type": "INTEGER", "nullable": true, "default": null, "autoincrement": false, "comment": null}}, "response_time": {"status": "PASS", "issues": [], "expected": {"type": "FLOAT", "nullable": true}, "actual": {"name": "response_time", "type": "DOUBLE PRECISION", "nullable": true, "default": null, "autoincrement": false, "comment": null}}, "success": {"status": "PASS", "issues": [], "expected": {"type": "BOOLEAN", "nullable": true, "default": true}, "actual": {"name": "success", "type": "BOOLEAN", "nullable": true, "default": null, "autoincrement": false, "comment": null}}, "error_message": {"status": "PASS", "issues": [], "expected": {"type": "TEXT", "nullable": true}, "actual": {"name": "error_message", "type": "TEXT", "nullable": true, "default": null, "autoincrement": false, "comment": null}}, "estimated_cost": {"status": "PASS", "issues": [], "expected": {"type": "FLOAT", "nullable": true}, "actual": {"name": "estimated_cost", "type": "DOUBLE PRECISION", "nullable": true, "default": null, "autoincrement": false, "comment": null}}, "created_at": {"status": "PASS", "issues": [], "expected": {"type": "TIMESTAMP", "nullable": true}, "actual": {"name": "created_at", "type": "TIMESTAMP", "nullable": true, "default": "now()", "autoincrement": false, "comment": null}}}, "indexes": {"ix_llm_usage_logs_id": {"status": "PASS"}}, "constraints": {}, "foreign_keys": {"llm_usage_logs_user_id_fkey": {"status": "PASS"}, "llm_usage_logs_config_id_fkey": {"status": "PASS"}}}, "prompt_templates": {"status": "PASS", "message": "", "columns": {"id": {"status": "PASS", "issues": [], "expected": {"type": "INTEGER", "nullable": false, "primary_key": true}, "actual": {"name": "id", "type": "INTEGER", "nullable": false, "default": "nextval('prompt_templates_id_seq'::regclass)", "autoincrement": true, "comment": null}}, "name": {"status": "PASS", "issues": [], "expected": {"type": "VARCHAR", "nullable": false, "unique": true, "length": 100}, "actual": {"name": "name", "type": "VARCHAR(100)", "nullable": false, "default": null, "autoincrement": false, "comment": "模板名称"}}, "template": {"status": "PASS", "issues": [], "expected": {"type": "TEXT", "nullable": false}, "actual": {"name": "template", "type": "TEXT", "nullable": false, "default": null, "autoincrement": false, "comment": "模板内容"}}, "description": {"status": "PASS", "issues": [], "expected": {"type": "TEXT", "nullable": true}, "actual": {"name": "description", "type": "TEXT", "nullable": true, "default": null, "autoincrement": false, "comment": "模板描述"}}, "version": {"status": "PASS", "issues": [], "expected": {"type": "INTEGER", "nullable": true, "default": 1}, "actual": {"name": "version", "type": "INTEGER", "nullable": true, "default": null, "autoincrement": false, "comment": "版本号"}}, "is_active": {"status": "PASS", "issues": [], "expected": {"type": "BOOLEAN", "nullable": true, "default": true}, "actual": {"name": "is_active", "type": "BOOLEAN", "nullable": true, "default": null, "autoincrement": false, "comment": "是否激活"}}, "created_at": {"status": "PASS", "issues": [], "expected": {"type": "TIMESTAMP", "nullable": true}, "actual": {"name": "created_at", "type": "TIMESTAMP", "nullable": true, "default": "now()", "autoincrement": false, "comment": "创建时间"}}, "updated_at": {"status": "PASS", "issues": [], "expected": {"type": "TIMESTAMP", "nullable": true}, "actual": {"name": "updated_at", "type": "TIMESTAMP", "nullable": true, "default": "now()", "autoincrement": false, "comment": "更新时间"}}}, "indexes": {"ix_prompt_templates_id": {"status": "PASS"}, "ix_prompt_templates_name": {"status": "PASS"}}, "constraints": {}, "foreign_keys": {}}}, "summary": {"total_tables": 9, "passed_tables": 9, "failed_tables": 0, "missing_tables": 0, "total_columns": 125, "passed_columns": 125, "failed_columns": 0, "missing_columns": 0, "total_constraints": 0, "passed_constraints": 0, "failed_constraints": 0, "total_indexes": 19, "passed_indexes": 19, "failed_indexes": 0}, "check_time": "2025-06-19T23:22:16.265492"}