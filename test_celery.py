#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Celery Worker是否正常工作
"""
import os
import sys
import time
import subprocess
from celery import Celery


def test_celery_connection():
    """测试Celery连接"""
    print("🔍 测试Celery连接...")
    
    try:
        # 创建Celery实例
        celery_app = Celery('test')
        celery_app.config_from_object({
            'broker_url': 'redis://localhost:6379/0',
            'result_backend': 'redis://localhost:6379/0',
        })
        
        # 测试连接
        inspect = celery_app.control.inspect()
        stats = inspect.stats()
        
        if stats:
            print("✅ Celery连接成功")
            print(f"📊 活跃Worker数量: {len(stats)}")
            for worker_name, worker_stats in stats.items():
                print(f"  - Worker: {worker_name}")
                print(f"    进程数: {worker_stats.get('pool', {}).get('processes', 'N/A')}")
        else:
            print("❌ 没有发现活跃的Worker")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Celery连接失败: {e}")
        return False


def test_redis_connection():
    """测试Redis连接"""
    print("\n🔍 测试Redis连接...")
    
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        print("✅ Redis连接成功")
        
        # 检查Redis信息
        info = r.info()
        print(f"📊 Redis版本: {info.get('redis_version', 'N/A')}")
        print(f"📊 连接数: {info.get('connected_clients', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Redis连接失败: {e}")
        return False


def start_celery_worker_test():
    """启动Celery Worker进行测试"""
    print("\n🔧 启动Celery Worker测试...")
    
    # 设置环境变量
    os.environ.setdefault('PYTHONPATH', os.getcwd())
    os.environ.setdefault('PYTHONIOENCODING', 'utf-8')
    
    # 构建启动命令
    cmd = [
        sys.executable, "-m", "celery",
        "-A", "app.core.celery_app:celery_app",
        "worker",
        "--loglevel=debug",  # 使用debug级别获取更多信息
        "--pool=solo",
        "--concurrency=1"   # 减少并发数
    ]
    
    print(f"📝 执行命令: {' '.join(cmd)}")
    print("⏳ 启动Worker，等待30秒...")
    
    try:
        # 启动Worker
        process = subprocess.Popen(
            cmd,
            cwd=os.getcwd(),
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1,
            encoding='utf-8',
            errors='replace'
        )
        
        print(f"✅ Worker进程已启动，PID: {process.pid}")
        
        # 读取输出30秒，检查Worker是否正常工作
        start_time = time.time()
        ready_found = False
        worker_working = False

        while time.time() - start_time < 30:
            try:
                line = process.stdout.readline()
                if line:
                    print(f"[WORKER] {line.strip()}")
                    line_lower = line.lower()

                    # 检查Worker准备就绪
                    if "ready" in line_lower:
                        ready_found = True
                        print("🎉 Worker已准备就绪！")
                        break

                    # 检查Worker是否在正常工作（处理任务或连接成功）
                    if any(keyword in line_lower for keyword in [
                        "connected to redis", "task update sent", "llm service initialized",
                        "starting new", "generation", "prefetch_count"
                    ]):
                        worker_working = True
                        print("✅ Worker正在正常工作")

                    # 如果Worker开始处理任务，说明它已经正常启动
                    if worker_working and time.time() - start_time > 10:
                        print("🎉 Worker正在处理任务，说明启动成功！")
                        ready_found = True
                        break

                else:
                    time.sleep(0.1)
            except Exception as e:
                print(f"读取输出错误: {e}")
                break
        
        # 终止进程
        process.terminate()
        try:
            process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            process.kill()
            process.wait()
        
        if ready_found:
            print("✅ Celery Worker测试成功")
            return True
        else:
            print("❌ Celery Worker未能正常启动")
            return False
            
    except Exception as e:
        print(f"❌ 启动Worker失败: {e}")
        return False


def main():
    """主函数"""
    print("🔧 AI小说生成器 - Celery Worker诊断工具")
    print("=" * 50)
    
    # 检查环境
    if not os.path.exists("app"):
        print("❌ 错误：请在项目根目录运行此脚本")
        sys.exit(1)
    
    print(f"✅ 工作目录: {os.getcwd()}")
    print(f"✅ Python解释器: {sys.executable}")
    
    # 执行测试
    redis_ok = test_redis_connection()
    celery_ok = test_celery_connection()
    worker_ok = start_celery_worker_test()
    
    print("\n" + "=" * 50)
    print("📋 诊断结果汇总:")
    print(f"  - Redis连接: {'✅ 正常' if redis_ok else '❌ 失败'}")
    print(f"  - Celery连接: {'✅ 正常' if celery_ok else '❌ 失败'}")
    print(f"  - Worker启动: {'✅ 正常' if worker_ok else '❌ 失败'}")
    
    if redis_ok and worker_ok:
        print("\n🎉 Celery Worker诊断通过！")
        print("💡 建议：可以正常使用Celery功能")
    else:
        print("\n⚠️ Celery Worker存在问题")
        if not redis_ok:
            print("💡 建议：检查Redis服务是否正常运行")
        if not worker_ok:
            print("💡 建议：检查Celery配置和依赖")


if __name__ == "__main__":
    main()
