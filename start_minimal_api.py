#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最简化的API启动脚本 - 用于调试
"""
import os
import sys
import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware


def create_minimal_app():
    """创建最简化的FastAPI应用"""
    app = FastAPI(
        title="AI Novel Generator - Debug",
        version="1.0.0",
        description="调试用最简化API"
    )
    
    # 添加CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    @app.get("/")
    async def root():
        """根路径"""
        return {
            "message": "AI Novel Generator API - Debug Mode",
            "status": "running"
        }
    
    @app.get("/health")
    async def health_check():
        """健康检查"""
        return {"status": "healthy", "mode": "debug"}
    
    @app.get("/test")
    async def test_endpoint():
        """测试端点"""
        return {"message": "Test endpoint working", "timestamp": "2025-06-21"}
    
    return app


def main():
    """主函数"""
    print("🚀 AI小说生成器 - 最简化API调试")
    print("=" * 40)
    
    # 设置环境变量
    os.environ.setdefault('PYTHONPATH', os.getcwd())
    
    print(f"✅ 工作目录: {os.getcwd()}")
    print(f"✅ Python解释器: {sys.executable}")
    print("🌐 启动最简化API服务...")
    print("📍 地址: http://localhost:8000")
    print("🔍 测试端点:")
    print("  - http://localhost:8000/")
    print("  - http://localhost:8000/health")
    print("  - http://localhost:8000/test")
    print("\n按 Ctrl+C 停止服务")
    print("=" * 40)
    
    try:
        app = create_minimal_app()
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8000,
            log_level="debug"
        )
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
