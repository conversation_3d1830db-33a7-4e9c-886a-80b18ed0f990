#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI小说生成器 - 仅启动API服务（用于调试）
"""
import os
import sys
import subprocess
import time
import requests


def check_api_ready(max_wait=60):
    """检查API是否准备就绪"""
    print("🔍 等待API服务启动...")
    
    start_time = time.time()
    while time.time() - start_time < max_wait:
        try:
            response = requests.get("http://localhost:8000/health", timeout=2)
            if response.status_code == 200:
                print("✅ API服务已准备就绪")
                return True
        except:
            pass
        
        print("⏳ 等待中...", end="\r")
        time.sleep(2)
    
    print(f"\n❌ API服务在 {max_wait} 秒内未能启动")
    return False


def main():
    """主函数"""
    print("🚀 AI小说生成器 - API服务调试启动")
    print("=" * 40)
    
    # 检查环境
    if not os.path.exists("app"):
        print("❌ 错误：请在项目根目录运行此脚本")
        sys.exit(1)
    
    # 设置环境变量
    os.environ.setdefault('PYTHONPATH', os.getcwd())
    os.environ.setdefault('PYTHONIOENCODING', 'utf-8')
    
    print(f"✅ 工作目录: {os.getcwd()}")
    print(f"✅ Python解释器: {sys.executable}")
    
    # 构建启动命令
    cmd = [
        sys.executable, "-m", "uvicorn",
        "app.main:app",
        "--host=0.0.0.0",
        "--port=8000",
        "--log-level=debug"  # 使用debug级别
    ]
    
    print(f"📝 执行命令: {' '.join(cmd)}")
    print("🌐 服务将在 http://localhost:8000 启动")
    print("📚 API文档: http://localhost:8000/docs")
    print("\n启动中...")
    print("=" * 40)
    
    try:
        # 启动API服务
        process = subprocess.Popen(
            cmd, 
            cwd=os.getcwd(),
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1,
            encoding='utf-8',
            errors='replace'
        )
        
        print(f"✅ API进程已启动，PID: {process.pid}")
        
        # 启动日志输出线程
        import threading
        
        def log_output():
            try:
                for line in iter(process.stdout.readline, ''):
                    if line.strip():
                        print(f"[API] {line.strip()}")
            except Exception as e:
                print(f"[API] 日志输出异常: {e}")
        
        log_thread = threading.Thread(target=log_output, daemon=True)
        log_thread.start()
        
        # 检查API是否准备就绪
        if check_api_ready():
            print("\n🎉 API服务启动成功！")
            print("🌐 你可以访问:")
            print("  - 健康检查: http://localhost:8000/health")
            print("  - API文档: http://localhost:8000/docs")
            print("  - 根路径: http://localhost:8000/")
            print("\n按 Ctrl+C 停止服务")
            print("=" * 40)
            
            # 等待用户中断
            try:
                process.wait()
            except KeyboardInterrupt:
                print("\n👋 用户中断，正在停止服务...")
        else:
            print("\n❌ API服务启动失败")
            
    except KeyboardInterrupt:
        print("\n👋 用户中断，正在停止服务...")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False
    finally:
        # 清理进程
        if 'process' in locals():
            try:
                process.terminate()
                process.wait(timeout=10)
                print("✅ API服务已停止")
            except subprocess.TimeoutExpired:
                process.kill()
                process.wait()
                print("✅ API服务已强制停止")
    
    return True


if __name__ == "__main__":
    main()
