#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
WebSocket消息类型测试
验证所有消息类型是否正确定义和处理
"""

import asyncio
import json
from datetime import datetime

# 测试WebSocket消息处理
async def test_websocket_message_handling():
    """测试WebSocket消息处理"""
    
    # 模拟各种WebSocket消息
    test_messages = [
        {
            "type": "task_update",
            "task_id": "test-task-123",
            "status": "IN_PROGRESS",
            "progress": 25,
            "message": "正在生成核心种子...",
            "current_stage": "core_seed",
            "timestamp": datetime.now().isoformat()
        },
        {
            "type": "stage_start",
            "task_id": "test-task-123",
            "stage": "core_seed",
            "timestamp": datetime.now().isoformat()
        },
        {
            "type": "content_chunk",
            "task_id": "test-task-123",
            "stage": "core_seed",
            "chunk": "这是一个测试内容片段，展示流式传输的效果...",
            "chunk_index": 1,
            "total_length": 500,
            "timestamp": datetime.now().isoformat()
        },
        {
            "type": "content_complete",
            "task_id": "test-task-123",
            "stage": "core_seed",
            "final_content": "这是完整的核心种子内容，包含了小说的基本设定和核心元素。",
            "timestamp": datetime.now().isoformat()
        },
        {
            "type": "generation_complete",
            "task_id": "test-task-123",
            "summary": "小说架构生成完成，共生成2500字符的架构文档",
            "result": {
                "word_count": 2500,
                "components": {
                    "core_seed": "核心种子内容...",
                    "character_dynamics": "角色动力学内容...",
                    "world_building": "世界观内容...",
                    "plot_architecture": "情节架构内容..."
                }
            },
            "timestamp": datetime.now().isoformat()
        },
        {
            "type": "error_recovery",
            "task_id": "test-task-123",
            "error_message": "网络连接超时",
            "recovery_action": "重试第2次",
            "timestamp": datetime.now().isoformat()
        }
    ]
    
    print("🧪 WebSocket消息处理测试")
    print("=" * 50)
    
    # 模拟消息处理函数
    async def handle_message(data):
        """模拟消息处理"""
        message_type = data.get("type", "unknown")
        
        if message_type == "task_update":
            status = data.get("status", "")
            progress = data.get("progress", 0)
            message = data.get("message", "")
            current_stage = data.get("current_stage", "")
            
            stage_info = f" [{current_stage}]" if current_stage else ""
            print(f"📈 任务进度{stage_info}: {progress}% - {message}")
            
        elif message_type == "stage_start":
            stage = data.get("stage", "")
            print(f"🎬 开始生成阶段: {stage}")
            
        elif message_type == "content_chunk":
            stage = data.get("stage", "")
            chunk = data.get("chunk", "")
            chunk_index = data.get("chunk_index", 0)
            total_length = data.get("total_length", 0)
            
            display_chunk = chunk[:50] + "..." if len(chunk) > 50 else chunk
            print(f"📝 [{stage}] 片段{chunk_index}: {display_chunk}")
            if total_length > 0:
                print(f"📊 [{stage}] 当前长度: {total_length}字符")
            
        elif message_type == "content_complete":
            stage = data.get("stage", "")
            final_content = data.get("final_content", "")
            
            content_length = len(final_content) if final_content else 0
            print(f"✅ 阶段完成: {stage} (共{content_length}字符)")
            
            if final_content:
                preview = final_content[:100] + "..." if len(final_content) > 100 else final_content
                print(f"📄 [{stage}] 内容预览: {preview}")
            
        elif message_type == "generation_complete":
            summary = data.get("summary", "")
            result = data.get("result", {})
            
            print(f"🎉 生成任务完成!")
            if summary:
                print(f"📋 完成摘要: {summary}")
            
            if result:
                if "word_count" in result:
                    print(f"📊 生成字数: {result['word_count']}")
                if "components" in result:
                    components = result["components"]
                    for component_name, content in components.items():
                        if content:
                            print(f"   {component_name}: {len(content)}字符")
            
        elif message_type == "error_recovery":
            error_message = data.get("error_message", "")
            recovery_action = data.get("recovery_action", "")
            print(f"⚠️ 错误恢复: {error_message}")
            if recovery_action:
                print(f"🔄 恢复操作: {recovery_action}")
        else:
            print(f"❓ 未知消息类型: {message_type}")
        
        print("-" * 30)
    
    # 处理所有测试消息
    for i, message in enumerate(test_messages, 1):
        print(f"\n消息 {i}:")
        await handle_message(message)
        await asyncio.sleep(0.5)  # 模拟时间间隔
    
    print("\n✅ 所有消息处理测试完成!")


def test_message_types():
    """测试消息类型定义"""
    print("🔍 消息类型定义测试")
    print("=" * 50)
    
    try:
        from app.core.websocket import MessageType
        
        print("✅ 成功导入 MessageType")
        print("📋 可用的消息类型:")
        
        for msg_type in MessageType:
            print(f"   - {msg_type.name}: {msg_type.value}")
        
        # 检查新增的消息类型
        required_types = [
            "TASK_UPDATE",
            "STAGE_START", 
            "CONTENT_CHUNK",
            "CONTENT_COMPLETE",
            "GENERATION_COMPLETE",
            "ERROR_RECOVERY"
        ]
        
        missing_types = []
        for req_type in required_types:
            if not hasattr(MessageType, req_type):
                missing_types.append(req_type)
        
        if missing_types:
            print(f"❌ 缺少消息类型: {missing_types}")
        else:
            print("✅ 所有必需的消息类型都已定义")
            
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
    except Exception as e:
        print(f"❌ 测试异常: {e}")


def test_websocket_functions():
    """测试WebSocket函数导出"""
    print("\n🔧 WebSocket函数导出测试")
    print("=" * 50)
    
    try:
        from app.core.websocket import (
            send_task_update,
            send_content_stream,
            start_streaming_stage,
            complete_streaming_stage,
            send_generation_complete,
            send_error_recovery
        )
        
        print("✅ 成功导入所有WebSocket函数:")
        print("   - send_task_update")
        print("   - send_content_stream") 
        print("   - start_streaming_stage")
        print("   - complete_streaming_stage")
        print("   - send_generation_complete")
        print("   - send_error_recovery")
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
    except Exception as e:
        print(f"❌ 测试异常: {e}")


async def main():
    """主测试函数"""
    print("🚀 WebSocket流式传输功能测试")
    print("=" * 60)
    
    # 测试1: 消息类型定义
    test_message_types()
    
    # 测试2: 函数导出
    test_websocket_functions()
    
    # 测试3: 消息处理
    await test_websocket_message_handling()
    
    print("\n🎉 所有测试完成!")


if __name__ == "__main__":
    asyncio.run(main())
